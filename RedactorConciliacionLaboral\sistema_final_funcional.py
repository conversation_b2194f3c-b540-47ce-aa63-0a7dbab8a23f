#!/usr/bin/env python3
"""
SISTEMA FINAL FUNCIONAL: Redactor de Conciliación Laboral
Versión simplificada que funciona de manera confiable con URLs, PDFs y exportación WordPress
"""

import subprocess
import re
import os
from pathlib import Path
from datetime import datetime, timezone
import json

def verificar_ollama():
    """Verifica que Ollama esté funcionando"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'llama3.1:8b' in result.stdout:
            print("✓ Ollama y modelo llama3.1:8b disponibles")
            return True
        else:
            print("✗ Modelo llama3.1:8b no encontrado")
            print("Ejecuta: ollama pull llama3.1:8b")
            return False
    except Exception as e:
        print(f"✗ Error verificando Ollama: {e}")
        return False

def extraer_texto_url(url):
    """Extrae texto de una URL usando trafilatura"""
    try:
        import trafilatura
        print(f"   📥 Descargando: {url}")
        downloaded = trafilatura.fetch_url(url)
        if downloaded:
            texto = trafilatura.extract(downloaded)
            if texto:
                print(f"   ✓ Extraído: {len(texto)} caracteres")
                return texto
        print(f"   ✗ No se pudo extraer texto")
        return None
    except ImportError:
        print("   ✗ trafilatura no instalado. Ejecuta: pip install trafilatura")
        return None
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return None

def extraer_texto_pdf(ruta_pdf):
    """Extrae texto de un PDF usando pdfplumber"""
    try:
        import pdfplumber
        print(f"   📄 Procesando PDF: {ruta_pdf}")
        texto_completo = ""
        
        with pdfplumber.open(ruta_pdf) as pdf:
            for pagina in pdf.pages:
                texto_pagina = pagina.extract_text()
                if texto_pagina:
                    texto_completo += texto_pagina + "\n"
        
        if texto_completo.strip():
            print(f"   ✓ Extraído: {len(texto_completo)} caracteres")
            return texto_completo.strip()
        else:
            print(f"   ✗ No se pudo extraer texto")
            return None
    except ImportError:
        print("   ✗ pdfplumber no instalado. Ejecuta: pip install pdfplumber")
        return None
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return None

def procesar_con_ollama(texto, tipo="organizar"):
    """Procesa texto con Ollama"""
    try:
        if tipo == "organizar":
            prompt = f"""Analiza este texto legal y responde EXACTAMENTE así:
TEMA: [una palabra como finiquito, vacaciones, jornada]
RESUMEN: [resumen de máximo 40 palabras]

Texto: {texto}"""
        else:
            prompt = f"""Crea un artículo SEO en markdown sobre este tema legal:

{texto}

Incluye:
- Título con #
- Introducción breve
- 2 secciones con ##
- Conclusión con llamado a acción

Máximo 300 palabras, en español."""
        
        cmd = ['ollama', 'run', 'llama3.1:8b', prompt]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return f"Error: {result.stderr}"
    except Exception as e:
        return f"Error: {str(e)}"

def convertir_markdown_a_wordpress(contenido_markdown):
    """Convierte markdown a HTML para WordPress"""
    html = contenido_markdown
    
    # Convertir títulos
    html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
    html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
    html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
    
    # Convertir negritas
    html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
    html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)
    
    # Convertir párrafos
    lineas = html.split('\n')
    html_lineas = []
    en_lista = False
    
    for linea in lineas:
        if re.match(r'^\s*[\*\-\+]\s+', linea):
            if not en_lista:
                html_lineas.append('<ul>')
                en_lista = True
            item = re.sub(r'^\s*[\*\-\+]\s+', '', linea)
            html_lineas.append(f'<li>{item}</li>')
        else:
            if en_lista:
                html_lineas.append('</ul>')
                en_lista = False
            if linea.strip() and not linea.startswith('<'):
                html_lineas.append(f'<p>{linea}</p>')
            elif linea.strip():
                html_lineas.append(linea)
    
    if en_lista:
        html_lineas.append('</ul>')
    
    return '\n'.join(html_lineas)

def procesar_fuentes(urls=None, pdfs=None):
    """Procesa múltiples URLs y PDFs"""
    print("=== PROCESANDO FUENTES MÚLTIPLES ===\n")
    
    contenidos = []
    
    # Procesar URLs
    if urls:
        print(f"📥 Procesando {len(urls)} URLs...")
        for i, url in enumerate(urls, 1):
            print(f"\n--- URL {i}/{len(urls)} ---")
            texto = extraer_texto_url(url)
            if texto:
                contenidos.append({
                    'fuente': url,
                    'tipo': 'URL',
                    'texto': texto
                })
    
    # Procesar PDFs
    if pdfs:
        print(f"\n📄 Procesando {len(pdfs)} PDFs...")
        for i, pdf_path in enumerate(pdfs, 1):
            print(f"\n--- PDF {i}/{len(pdfs)} ---")
            if os.path.exists(pdf_path):
                texto = extraer_texto_pdf(pdf_path)
                if texto:
                    contenidos.append({
                        'fuente': Path(pdf_path).name,
                        'tipo': 'PDF',
                        'texto': texto
                    })
            else:
                print(f"   ✗ Archivo no encontrado: {pdf_path}")
    
    return contenidos

def generar_articulos_wordpress(contenidos):
    """Genera artículos listos para WordPress"""
    print(f"\n🤖 Generando artículos con Ollama...")
    
    articulos = []
    
    for i, contenido in enumerate(contenidos, 1):
        print(f"\n--- Procesando {i}/{len(contenidos)}: {contenido['fuente'][:50]}... ---")
        
        # Organizar contenido
        print("   🔍 Extrayendo tema y resumen...")
        resultado_org = procesar_con_ollama(contenido['texto'], "organizar")
        
        # Extraer tema y resumen
        tema = "derecho_laboral"
        resumen = contenido['texto'][:50] + "..."
        
        if "TEMA:" in resultado_org:
            try:
                tema_match = re.search(r'TEMA:\s*([^\n]+)', resultado_org, re.IGNORECASE)
                if tema_match:
                    tema = tema_match.group(1).strip().replace('*', '').lower()
                
                resumen_match = re.search(r'RESUMEN:\s*([^\n]+)', resultado_org, re.IGNORECASE)
                if resumen_match:
                    resumen = resumen_match.group(1).strip().replace('*', '')
            except:
                pass
        
        print(f"   ✓ Tema: {tema}")
        print(f"   ✓ Resumen: {resumen[:50]}...")
        
        # Generar artículo
        print("   📝 Generando artículo SEO...")
        contexto = f"Tema: {tema}\nResumen: {resumen}\nTexto: {contenido['texto']}"
        articulo_markdown = procesar_con_ollama(contexto, "articulo")
        
        # Convertir a HTML
        articulo_html = convertir_markdown_a_wordpress(articulo_markdown)
        
        # Extraer título
        titulo_match = re.search(r'^#\s*(.+)$', articulo_markdown, re.MULTILINE)
        titulo = titulo_match.group(1) if titulo_match else f"Guía sobre {tema}"
        titulo = re.sub(r'\*\*(.+?)\*\*', r'\1', titulo)  # Limpiar markdown
        
        # Generar metadatos SEO
        slug = re.sub(r'[^\w\s-]', '', titulo.lower())
        slug = re.sub(r'[-\s]+', '-', slug).strip('-')
        
        meta_description = resumen[:157] + "..." if len(resumen) > 160 else resumen
        keywords = f"{tema}, derecho laboral, conciliación laboral, ley trabajo"
        
        print(f"   ✓ Artículo: {titulo}")
        
        # Crear artículo completo
        articulo = {
            'fuente_original': contenido['fuente'],
            'tipo_fuente': contenido['tipo'],
            'tema': tema,
            'resumen': resumen,
            'titulo': titulo,
            'slug': slug,
            'meta_description': meta_description,
            'keywords': keywords,
            'articulo_markdown': articulo_markdown,
            'articulo_html': articulo_html,
            'fecha_procesamiento': datetime.now(timezone.utc)
        }
        
        articulos.append(articulo)
    
    return articulos

def exportar_wordpress(articulos):
    """Exporta artículos para WordPress"""
    print(f"\n📰 Exportando {len(articulos)} artículos para WordPress...")
    
    # Crear directorio de exportación
    export_dir = Path("exports_wordpress_final")
    export_dir.mkdir(exist_ok=True)
    
    # Exportar cada artículo
    for i, articulo in enumerate(articulos, 1):
        # Crear archivo WordPress
        wordpress_content = f"""<!-- ARTÍCULO PARA WORDPRESS -->
<!-- Título: {articulo['titulo']} -->
<!-- Slug: {articulo['slug']} -->
<!-- Meta Description: {articulo['meta_description']} -->
<!-- Keywords: {articulo['keywords']} -->
<!-- Categoría: Derecho Laboral -->

{articulo['articulo_html']}

<!-- Fuente: {articulo['fuente_original']} ({articulo['tipo_fuente']}) -->
<!-- Tema: {articulo['tema']} -->
<!-- Generado: {articulo['fecha_procesamiento']} -->
"""
        
        # Guardar archivo
        nombre_archivo = f"{articulo['slug']}.html"
        ruta_archivo = export_dir / nombre_archivo
        
        with open(ruta_archivo, 'w', encoding='utf-8') as f:
            f.write(wordpress_content)
        
        print(f"   ✓ {articulo['titulo']} → {nombre_archivo}")
    
    # Crear archivo resumen
    with open(export_dir / "resumen_articulos.txt", 'w', encoding='utf-8') as f:
        f.write("RESUMEN DE ARTÍCULOS PARA WORDPRESS\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total: {len(articulos)} artículos\n\n")
        
        for i, articulo in enumerate(articulos, 1):
            f.write(f"{i}. {articulo['titulo']}\n")
            f.write(f"   Archivo: {articulo['slug']}.html\n")
            f.write(f"   Fuente: {articulo['fuente_original']} ({articulo['tipo_fuente']})\n")
            f.write(f"   Tema: {articulo['tema']}\n")
            f.write(f"   Meta: {articulo['meta_description']}\n")
            f.write(f"   Keywords: {articulo['keywords']}\n\n")
        
        f.write("INSTRUCCIONES PARA WORDPRESS:\n")
        f.write("1. Abre cada archivo .html\n")
        f.write("2. Copia el contenido HTML (sin comentarios)\n")
        f.write("3. En WordPress:\n")
        f.write("   - Crear nueva entrada\n")
        f.write("   - Pegar título\n")
        f.write("   - Cambiar a modo HTML y pegar contenido\n")
        f.write("   - Configurar slug en Permalink\n")
        f.write("   - Agregar meta description en plugin SEO\n")
        f.write("   - Agregar keywords como etiquetas\n")
        f.write("   - Asignar categoría 'Derecho Laboral'\n")
        f.write("   - Publicar\n")
    
    print(f"✓ Exportación completada en: {export_dir.absolute()}")
    return export_dir

def main():
    """Función principal del sistema"""
    print("=== SISTEMA REDACTOR CONCILIACIÓN LABORAL ===\n")
    
    if not verificar_ollama():
        return
    
    # Configurar fuentes
    print("📋 CONFIGURACIÓN DE FUENTES")
    print("-" * 30)
    
    # URLs de ejemplo
    urls_ejemplo = [
        "https://www.dt.gob.cl/legislacion/1611/w3-article-59096.html",
        "https://www.bcn.cl/leychile/navegar?idNorma=207436"
    ]
    
    # PDFs de ejemplo (puedes agregar rutas reales)
    pdfs_ejemplo = [
        # "documentos/codigo_trabajo.pdf",
        # "documentos/manual_vacaciones.pdf"
    ]
    
    print("URLs configuradas:")
    for url in urls_ejemplo:
        print(f"  - {url}")
    
    if pdfs_ejemplo:
        print("PDFs configurados:")
        for pdf in pdfs_ejemplo:
            print(f"  - {pdf}")
    
    respuesta = input(f"\n¿Procesar estas fuentes? (s/n): ").lower()
    if respuesta != 's':
        print("Operación cancelada")
        return
    
    # Procesar fuentes
    contenidos = procesar_fuentes(urls_ejemplo, pdfs_ejemplo)
    
    if not contenidos:
        print("✗ No se pudo procesar ningún contenido")
        return
    
    # Generar artículos
    articulos = generar_articulos_wordpress(contenidos)
    
    if not articulos:
        print("✗ No se pudo generar ningún artículo")
        return
    
    # Exportar para WordPress
    export_dir = exportar_wordpress(articulos)
    
    # Mostrar resultados
    print(f"\n🎉 ¡SISTEMA COMPLETADO EXITOSAMENTE!")
    print("=" * 50)
    print(f"📊 ESTADÍSTICAS:")
    print(f"   📥 Fuentes procesadas: {len(contenidos)}")
    print(f"   📰 Artículos generados: {len(articulos)}")
    print(f"   📁 Exportados a: {export_dir}")
    
    print(f"\n📋 ARTÍCULOS GENERADOS:")
    for i, articulo in enumerate(articulos, 1):
        print(f"   {i}. {articulo['titulo']}")
        print(f"      Fuente: {articulo['tipo_fuente']} - {articulo['fuente_original']}")
        print(f"      Archivo: {articulo['slug']}.html")
    
    print(f"\n✅ SISTEMA LISTO PARA USO CONTINUO")
    print("Para procesar más contenido:")
    print("1. Modifica las listas urls_ejemplo y pdfs_ejemplo")
    print("2. Ejecuta el script nuevamente")
    print("3. Los artículos se exportarán automáticamente para WordPress")

if __name__ == "__main__":
    main()
