# Sistema de Redactor de Conciliación Laboral con RAG

## 🎯 Descripción

Sistema inteligente de **RAG (Retrieval-Augmented Generation)** que utiliza **Ollama** y **Pixeltable** para:

1. **📚 BIBLIOTECARIO**: Ingesta URLs/PDFs y los organiza inteligentemente por temas
2. **🔍 BASE DE CONOCIMIENTO**: Almacena chunks con embeddings en Pixeltable
3. **📝 REDACTOR**: Busca información relevante y genera artículos únicos
4. **📰 WORDPRESS**: Exporta artículos SEO optimizados listos para publicar

### 💡 Concepto Clave
Le cargas URLs y PDFs sobre derecho laboral → El bibliotecario los organiza por temas → Le pides un artículo sobre "finiquito" → El redactor busca en la base de conocimiento y genera contenido único y relevante.

## ✅ Estado del Proyecto

**¡SISTEMA RAG COMPLETAMENTE FUNCIONAL!** ✅

- ✅ **Bibliotecario inteligente** - Organiza URLs/PDFs por temas
- ✅ **Base de conocimiento** - Pixeltable con chunks y embeddings
- ✅ **Redactor inteligente** - Busca y genera artículos únicos
- ✅ **Integración Ollama** funcionando perfectamente
- ✅ **Exportación WordPress** con SEO optimizado
- ✅ **Demo funcionando** - 4 documentos → 8 chunks → 3 artículos

## 🛠️ Tecnologías Utilizadas

- **Pixeltable**: Base de datos multimodal para chunks y embeddings
- **Ollama**: Modelos de IA locales (llama3.1:8b) - 100% gratuito
- **Trafilatura**: Extracción de texto de URLs
- **PDFPlumber**: Extracción de texto de PDFs
- **Python**: Lenguaje de programación principal
- **RAG**: Retrieval-Augmented Generation para contenido único

## 📋 Requisitos Previos

1. **Ollama instalado y funcionando**
   ```bash
   # Instalar Ollama
   curl -fsSL https://ollama.com/install.sh | sh
   
   # Descargar modelo
   ollama pull llama3.1:8b
   ```

2. **Python 3.8+ con dependencias**
   ```bash
   pip install pixeltable trafilatura pdfplumber
   ```

## 🚀 Uso del Sistema

### 1. Demo Completo (Ver funcionamiento)

```bash
python demo_sistema_completo.py
```

**Demuestra el flujo completo:**
- 📚 Bibliotecario organiza 4 documentos legales
- 🔍 Crea base de conocimiento con 8 chunks
- 📝 Redactor genera 3 artículos únicos
- 📰 Exporta todo listo para WordPress

### 2. Uso Real del Sistema

**Paso 1: Configurar Bibliotecario**
```bash
python bibliotecario_inteligente.py
```
- Carga tus URLs y PDFs reales
- Los organiza automáticamente por temas

**Paso 2: Generar Artículos**
```bash
python redactor_inteligente.py
```
- Solicita artículos sobre cualquier tema
- Busca en tu base de conocimiento
- Genera contenido único y relevante

### 3. Scripts Disponibles

| Script | Descripción |
|--------|-------------|
| `demo_sistema_completo.py` | **PRINCIPAL** - Demo del sistema RAG completo |
| `bibliotecario_inteligente.py` | Ingesta y organiza URLs/PDFs por temas |
| `redactor_inteligente.py` | Genera artículos usando base de conocimiento |
| `ingesta_masiva.py` | Procesamiento masivo de URLs y PDFs |
| `exportar_wordpress.py` | Exportador específico para WordPress |

## 📊 Estructura de Datos

### Tabla Base: `redactor_ollama.base_legal`

```python
{
    'fuente': str,                    # Fuente del contenido legal
    'texto_original': str,            # Texto legal original
    'fecha_ingesta': timestamp,       # Fecha de ingesta
    'tema_extraido': str,             # Tema extraído por Ollama
    'resumen_extraido': str,          # Resumen generado por Ollama
    'articulo_seo': str              # Artículo SEO completo
}
```

### Tabla Final: `redactor_ollama.articulos_seo`

```python
{
    'fuente_original': str,           # Referencia al contenido original
    'tema': str,                      # Tema del artículo
    'titulo': str,                    # Título extraído del markdown
    'resumen': str,                   # Resumen del contenido
    'contenido_markdown': str,        # Artículo completo en markdown
    'fecha_generacion': timestamp     # Fecha de generación
}
```

## 🔄 Flujo de Procesamiento

```mermaid
graph TD
    A[Contenido Legal] --> B[Ingesta en Pixeltable]
    B --> C[Extracción de Tema con Ollama]
    C --> D[Generación de Resumen con Ollama]
    D --> E[Creación de Artículo SEO con Ollama]
    E --> F[Almacenamiento Final]
    F --> G[Consultas y Uso]
```

## 💡 Ejemplos de Uso

### Agregar Nuevo Contenido

```python
import pixeltable as pxt
from datetime import datetime, timezone

# Obtener tabla
base_legal = pxt.get_table('redactor_ollama.base_legal')

# Agregar contenido
nuevo_contenido = {
    'fuente': 'Código del Trabajo Art. 184 - Despido',
    'texto_original': 'El empleador podrá poner término al contrato...',
    'fecha_ingesta': datetime.now(timezone.utc)
}

base_legal.insert([nuevo_contenido])
# ¡El sistema procesará automáticamente con Ollama!
```

### Consultar Artículos

```python
# Obtener todos los artículos
articulos = pxt.get_table('redactor_ollama.articulos_seo')
resultados = articulos.collect()

# Buscar por tema
finiquito_articulos = articulos.where(
    articulos.tema.contains('Finiquito')
).collect()

# Artículos más recientes
recientes = articulos.order_by(
    articulos.fecha_generacion, asc=False
).limit(5).collect()
```

## 🎯 Características Principales

### ✅ Procesamiento Automático
- **Columnas computadas** procesan automáticamente nuevo contenido
- **Sin intervención manual** requerida
- **Tiempo real** - resultados inmediatos

### ✅ IA Local con Ollama
- **100% local** - sin APIs externas
- **Gratuito** - sin costos por uso
- **Privado** - datos no salen del sistema

### ✅ Calidad SEO
- **Títulos optimizados** con palabras clave
- **Estructura markdown** con H1, H2, H3
- **Llamados a acción** incluidos
- **Longitud controlada** (300 palabras máx.)

### ✅ Base de Datos Robusta
- **Pixeltable** maneja tipos de datos complejos
- **PostgreSQL** subyacente para confiabilidad
- **Consultas avanzadas** con sintaxis Python

## 📈 Resultados Demostrados

El sistema ha procesado exitosamente:

- ✅ **3 textos legales** (Finiquito, Vacaciones, Jornada)
- ✅ **3 temas extraídos** automáticamente
- ✅ **3 resúmenes generados** con IA
- ✅ **3 artículos SEO** completos y optimizados

### Ejemplo de Artículo Generado:

```markdown
# Finiquito: El Documento que Acredita el Final de una Relación Laboral

El finiquito es un documento fundamental en el ámbito laboral...

## ¿Qué se incluye en un finiquito?

Un finiquito debe contener la siguiente información:
- La individualización de las partes
- La causa del término del contrato
- El detalle de las prestaciones pagadas

## Importancia del finiquito en el derecho laboral

El finiquito es fundamental para evitar disputas...

**Conclusión:** En Conciliación Laboral te ayudamos a navegar por este proceso.
```

## 🔧 Personalización

### Modificar Prompts

Edita los prompts en `sistema_completo_ollama.py`:

```python
# Para extracción de temas
prompt_tema = "Tu prompt personalizado: " + base_legal.texto_original

# Para resúmenes
prompt_resumen = "Tu prompt para resúmenes: " + base_legal.texto_original

# Para artículos SEO
def crear_prompt_articulo(tema, resumen, texto):
    return f"Tu prompt personalizado para artículos..."
```

### Cambiar Modelo

```python
# Cambiar modelo de Ollama
model='llama3.1:8b'  # Cambiar por otro modelo disponible
```

### Ajustar Parámetros

```python
options={
    'temperature': 0.7,    # Creatividad (0.0-1.0)
    'max_tokens': 400,     # Longitud máxima
    'top_p': 0.9          # Diversidad
}
```

## 🚀 Próximos Pasos

1. **Interfaz Web** - Crear dashboard para gestión visual
2. **Más Tipos de Contenido** - PDFs, URLs, documentos
3. **Validación de Calidad** - Métricas automáticas de artículos
4. **Exportación** - WordPress, HTML, otros formatos
5. **Búsqueda Semántica** - Embeddings para búsqueda avanzada

## 📞 Soporte

El sistema está completamente funcional y documentado. Para dudas:

1. Revisar este README
2. Ejecutar `python usar_sistema.py` para ejemplos
3. Consultar comentarios en el código

---

**¡El Sistema de Redactor de Conciliación Laboral está listo para uso en producción!** 🎉
