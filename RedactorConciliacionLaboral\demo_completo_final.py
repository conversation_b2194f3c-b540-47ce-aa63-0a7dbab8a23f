#!/usr/bin/env python3
"""
DEMO COMPLETO FINAL: Sistema Redactor con contenido de ejemplo realista
Demuestra el flujo completo con contenido legal simulado y exportación WordPress
"""

import subprocess
import re
import os
from pathlib import Path
from datetime import datetime, timezone
import time

def verificar_ollama():
    """Verifica que Ollama esté funcionando"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'llama3.1:8b' in result.stdout:
            print("✓ Ollama y modelo llama3.1:8b disponibles")
            return True
        else:
            print("✗ Modelo llama3.1:8b no encontrado")
            return False
    except Exception as e:
        print(f"✗ Error verificando Ollama: {e}")
        return False

def procesar_con_ollama(texto, tipo="organizar", timeout=120):
    """Procesa texto con Ollama con timeout extendido"""
    try:
        if tipo == "organizar":
            prompt = f"""Analiza este texto legal y responde EXACTAMENTE así:
TEMA: [una palabra como finiquito, vacaciones, jornada]
RESUMEN: [resumen de máximo 40 palabras]

Texto: {texto[:500]}"""  # Limitar texto para evitar timeouts
        else:
            prompt = f"""Crea un artículo SEO en markdown sobre este tema legal:

{texto}

Incluye:
- Título con #
- Introducción breve
- 2 secciones con ##
- Conclusión con llamado a acción

Máximo 250 palabras, en español."""
        
        print(f"   🤖 Procesando con Ollama (timeout: {timeout}s)...")
        cmd = ['ollama', 'run', 'llama3.1:8b', prompt]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return f"Error: {result.stderr}"
    except subprocess.TimeoutExpired:
        return "Error: Timeout - Ollama tardó demasiado en responder"
    except Exception as e:
        return f"Error: {str(e)}"

def convertir_markdown_a_wordpress(contenido_markdown):
    """Convierte markdown a HTML para WordPress"""
    html = contenido_markdown
    
    # Convertir títulos
    html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
    html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
    html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
    
    # Convertir negritas
    html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
    html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)
    
    # Convertir párrafos
    lineas = html.split('\n')
    html_lineas = []
    en_lista = False
    
    for linea in lineas:
        if re.match(r'^\s*[\*\-\+]\s+', linea):
            if not en_lista:
                html_lineas.append('<ul>')
                en_lista = True
            item = re.sub(r'^\s*[\*\-\+]\s+', '', linea)
            html_lineas.append(f'<li>{item}</li>')
        else:
            if en_lista:
                html_lineas.append('</ul>')
                en_lista = False
            if linea.strip() and not linea.startswith('<'):
                html_lineas.append(f'<p>{linea}</p>')
            elif linea.strip():
                html_lineas.append(linea)
    
    if en_lista:
        html_lineas.append('</ul>')
    
    return '\n'.join(html_lineas)

def demo_completo():
    """Demo completo con contenido legal realista"""
    print("=== DEMO COMPLETO: REDACTOR CONCILIACIÓN LABORAL ===\n")
    
    if not verificar_ollama():
        return
    
    # Contenido legal realista simulando URLs y PDFs
    contenidos_demo = [
        {
            'fuente': 'URL: https://www.dt.gob.cl/legislacion/1611/w3-article-59096.html',
            'tipo': 'URL',
            'texto': '''El contrato de trabajo es una convención por la cual el empleador y el trabajador se obligan recíprocamente, éste a prestar servicios personales bajo dependencia y subordinación del primero, y aquél a pagar por estos servicios una remuneración determinada. El contrato de trabajo puede ser individual o colectivo. Es individual el que se celebra entre un empleador y un trabajador. Es colectivo el que se celebra entre uno o más empleadores con una o más organizaciones sindicales o con trabajadores que se unan para negociar colectivamente.'''
        },
        {
            'fuente': 'PDF: Manual_Vacaciones_2024.pdf',
            'tipo': 'PDF',
            'texto': '''Las vacaciones anuales son un derecho irrenunciable del trabajador. Todo trabajador con más de un año de servicios tendrá derecho a un feriado anual de quince días hábiles, con remuneración íntegra que se pagará antes del inicio del período de vacaciones. El trabajador y el empleador podrán acordar que aquél haga uso de sus vacaciones en forma fraccionada. El feriado se otorgará de preferencia en primavera o verano, considerándose las necesidades del servicio.'''
        },
        {
            'fuente': 'URL: https://www.bcn.cl/leychile/navegar?idNorma=207436',
            'tipo': 'URL',
            'texto': '''La jornada ordinaria de trabajo no podrá exceder de cuarenta y cinco horas semanales. Podrá distribuirse en no más de seis ni en menos de cinco días. En ningún caso la jornada ordinaria podrá exceder de diez horas diarias. Quedan excluidos de la limitación de jornada de trabajo los trabajadores que se desempeñen en cargos de dirección o de confianza, tales como gerentes, administradores, apoderados con facultades de administración y todos aquellos que trabajen sin fiscalización superior inmediata.'''
        },
        {
            'fuente': 'PDF: Guia_Finiquito_Laboral.pdf',
            'tipo': 'PDF',
            'texto': '''El finiquito es el documento que acredita el término de la relación laboral y la liquidación de las prestaciones que correspondan al trabajador. Debe contener la individualización de las partes, la causa del término del contrato, el detalle de las prestaciones pagadas y la firma de ambas partes. El finiquito ratificado ante el inspector del trabajo produce el efecto de finiquitar todas las obligaciones laborales y previsionales derivadas del contrato de trabajo, sin perjuicio de las acciones de que sea titular el trabajador por concepto de indemnización por años de servicio.'''
        }
    ]
    
    print(f"📋 CONTENIDO A PROCESAR:")
    print(f"   📥 {len(contenidos_demo)} fuentes (URLs + PDFs)")
    for i, contenido in enumerate(contenidos_demo, 1):
        print(f"   {i}. {contenido['tipo']}: {contenido['fuente']}")
    
    print(f"\n🚀 INICIANDO PROCESAMIENTO...")
    
    # Procesar cada contenido
    articulos = []
    
    for i, contenido in enumerate(contenidos_demo, 1):
        print(f"\n--- Procesando {i}/{len(contenidos_demo)}: {contenido['fuente'][:60]}... ---")
        
        # Organizar contenido
        print("   🔍 Extrayendo tema y resumen...")
        resultado_org = procesar_con_ollama(contenido['texto'], "organizar", timeout=90)
        
        # Extraer tema y resumen
        tema = "derecho_laboral"
        resumen = contenido['texto'][:60] + "..."
        
        if "TEMA:" in resultado_org and "Error:" not in resultado_org:
            try:
                tema_match = re.search(r'TEMA:\s*([^\n]+)', resultado_org, re.IGNORECASE)
                if tema_match:
                    tema = tema_match.group(1).strip().replace('*', '').lower()
                
                resumen_match = re.search(r'RESUMEN:\s*([^\n]+)', resultado_org, re.IGNORECASE)
                if resumen_match:
                    resumen = resumen_match.group(1).strip().replace('*', '')
            except:
                pass
        
        print(f"   ✓ Tema: {tema}")
        print(f"   ✓ Resumen: {resumen[:50]}...")
        
        # Generar artículo
        print("   📝 Generando artículo SEO...")
        contexto = f"Tema: {tema}\nResumen: {resumen}\nTexto: {contenido['texto']}"
        articulo_markdown = procesar_con_ollama(contexto, "articulo", timeout=120)
        
        # Si hay error, usar plantilla por defecto
        if "Error:" in articulo_markdown or "Timeout" in articulo_markdown:
            print("   ⚠️  Usando plantilla por defecto debido a timeout")
            articulo_markdown = f"""# {tema.replace('_', ' ').title()}: Guía Completa

## Introducción

El {tema.replace('_', ' ')} es un aspecto fundamental del derecho laboral que todo trabajador y empleador debe conocer.

{resumen}

## Aspectos Legales Importantes

La legislación laboral establece claramente los derechos y obligaciones relacionados con {tema.replace('_', ' ')}.

## Recomendaciones Prácticas

Es importante conocer estos aspectos para evitar conflictos laborales y garantizar el cumplimiento de la normativa.

## Conclusión

Para más información sobre {tema.replace('_', ' ')} y otros temas de derecho laboral, consulta nuestros especialistas en conciliacionlaboral.net."""
        
        # Convertir a HTML
        articulo_html = convertir_markdown_a_wordpress(articulo_markdown)
        
        # Extraer título
        titulo_match = re.search(r'^#\s*(.+)$', articulo_markdown, re.MULTILINE)
        titulo = titulo_match.group(1) if titulo_match else f"Guía sobre {tema.replace('_', ' ').title()}"
        titulo = re.sub(r'\*\*(.+?)\*\*', r'\1', titulo)  # Limpiar markdown
        
        # Generar metadatos SEO
        slug = re.sub(r'[^\w\s-]', '', titulo.lower())
        slug = re.sub(r'[-\s]+', '-', slug).strip('-')
        
        meta_description = resumen[:157] + "..." if len(resumen) > 160 else resumen
        keywords = f"{tema.replace('_', ' ')}, derecho laboral, conciliación laboral, ley trabajo"
        
        print(f"   ✓ Artículo: {titulo}")
        
        # Crear artículo completo
        articulo = {
            'fuente_original': contenido['fuente'],
            'tipo_fuente': contenido['tipo'],
            'tema': tema,
            'resumen': resumen,
            'titulo': titulo,
            'slug': slug,
            'meta_description': meta_description,
            'keywords': keywords,
            'articulo_markdown': articulo_markdown,
            'articulo_html': articulo_html,
            'fecha_procesamiento': datetime.now(timezone.utc)
        }
        
        articulos.append(articulo)
        
        # Pausa entre procesamiento para no sobrecargar Ollama
        if i < len(contenidos_demo):
            print("   ⏱️  Pausa de 3 segundos...")
            time.sleep(3)
    
    # Exportar para WordPress
    print(f"\n📰 EXPORTANDO PARA WORDPRESS...")
    
    # Crear directorio de exportación
    export_dir = Path("exports_wordpress_demo_final")
    export_dir.mkdir(exist_ok=True)
    
    # Exportar cada artículo
    for i, articulo in enumerate(articulos, 1):
        # Crear archivo WordPress
        wordpress_content = f"""<!-- ARTÍCULO PARA WORDPRESS -->
<!-- Título: {articulo['titulo']} -->
<!-- Slug: {articulo['slug']} -->
<!-- Meta Description: {articulo['meta_description']} -->
<!-- Keywords: {articulo['keywords']} -->
<!-- Categoría: Derecho Laboral -->

{articulo['articulo_html']}

<!-- Fuente: {articulo['fuente_original']} ({articulo['tipo_fuente']}) -->
<!-- Tema: {articulo['tema']} -->
<!-- Generado: {articulo['fecha_procesamiento']} -->
"""
        
        # Guardar archivo
        nombre_archivo = f"{articulo['slug']}.html"
        ruta_archivo = export_dir / nombre_archivo
        
        with open(ruta_archivo, 'w', encoding='utf-8') as f:
            f.write(wordpress_content)
        
        print(f"   ✓ {articulo['titulo']} → {nombre_archivo}")
    
    # Crear archivo resumen
    with open(export_dir / "resumen_articulos.txt", 'w', encoding='utf-8') as f:
        f.write("RESUMEN DE ARTÍCULOS PARA WORDPRESS\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total: {len(articulos)} artículos\n\n")
        
        for i, articulo in enumerate(articulos, 1):
            f.write(f"{i}. {articulo['titulo']}\n")
            f.write(f"   Archivo: {articulo['slug']}.html\n")
            f.write(f"   Fuente: {articulo['fuente_original']} ({articulo['tipo_fuente']})\n")
            f.write(f"   Tema: {articulo['tema']}\n")
            f.write(f"   Meta: {articulo['meta_description']}\n")
            f.write(f"   Keywords: {articulo['keywords']}\n\n")
        
        f.write("INSTRUCCIONES PARA WORDPRESS:\n")
        f.write("1. Abre cada archivo .html\n")
        f.write("2. Copia el contenido HTML (sin comentarios)\n")
        f.write("3. En WordPress:\n")
        f.write("   - Crear nueva entrada\n")
        f.write("   - Pegar título\n")
        f.write("   - Cambiar a modo HTML y pegar contenido\n")
        f.write("   - Configurar slug en Permalink\n")
        f.write("   - Agregar meta description en plugin SEO\n")
        f.write("   - Agregar keywords como etiquetas\n")
        f.write("   - Asignar categoría 'Derecho Laboral'\n")
        f.write("   - Publicar en conciliacionlaboral.net\n")
    
    # Mostrar resultados finales
    print(f"\n🎉 ¡DEMO COMPLETADO EXITOSAMENTE!")
    print("=" * 50)
    print(f"📊 ESTADÍSTICAS FINALES:")
    print(f"   📥 Fuentes procesadas: {len(contenidos_demo)} (URLs + PDFs)")
    print(f"   🤖 Procesamiento: Ollama llama3.1:8b")
    print(f"   📰 Artículos generados: {len(articulos)}")
    print(f"   📁 Exportados a: {export_dir.absolute()}")
    
    print(f"\n📋 ARTÍCULOS GENERADOS:")
    for i, articulo in enumerate(articulos, 1):
        print(f"   {i}. {articulo['titulo']}")
        print(f"      Fuente: {articulo['tipo_fuente']} - {articulo['fuente_original'][:50]}...")
        print(f"      Archivo: {articulo['slug']}.html")
        print(f"      Tema: {articulo['tema']}")
    
    # Mostrar ejemplo de artículo
    if articulos:
        print(f"\n📄 EJEMPLO DE ARTÍCULO WORDPRESS:")
        ejemplo = articulos[0]
        print(f"Título: {ejemplo['titulo']}")
        print(f"Slug: {ejemplo['slug']}")
        print(f"Meta: {ejemplo['meta_description'][:60]}...")
        print(f"Keywords: {ejemplo['keywords']}")
        print(f"HTML: {ejemplo['articulo_html'][:200]}...")
    
    print(f"\n✅ SISTEMA LISTO PARA USO REAL")
    print("Para procesar URLs y PDFs reales:")
    print("1. Modifica la lista contenidos_demo con tus URLs y PDFs")
    print("2. Ejecuta el script")
    print("3. Copia los artículos de exports_wordpress_demo_final/ a WordPress")
    print("4. Publica en conciliacionlaboral.net")

if __name__ == "__main__":
    demo_completo()
