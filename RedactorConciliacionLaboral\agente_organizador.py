import pixeltable as pxt
from pixelagent.openai import Agent
from datetime import datetime

# Asumimos que ya existe la tabla BaseLegal creada en ingesta
tbl = pxt.get_table("redactor_legal.BaseLegal")

# Función auxiliar para enviar entry actual al agente
def formatear_prompt(texto: str):
    return f"""
Estás organizando contenido legal-laboral para crear una base de conocimiento SEO.
1) Indica el tema principal, un único tema breve (como: "vacaciones", "finiquito", etc.).
2) Proporciona un resumen de máximo 60 palabras.
3) Si encuentras una fecha en el texto (día, mes, año), indícalo en formato YYYY-MM-DD, si no, devuelve vacío.
Formato de salida JSON:
{{ "tema": "...", "resumen": "...", "fecha_doc": "YYYY-MM-DD" }}
Texto de referencia:
---
{texto}
---
"""

# Crear el agente organizador
agent = Agent(
    name="organizador",
    system_prompt="Actúa como organizador inteligente de textos legales laborales para SEO."
)

# Procesar entradas sin tema ni resumen
for row in tbl.where((tbl.tema == '') | (tbl.resumen == '')).collect():
    resultado = agent.chat(formatear_prompt(row['texto_original']))
    # Analizamos respuesta JSON
    import json
    datos = json.loads(resultado)
    # Guardamos datos organizados en la tabla
    tbl.where(tbl._id == row['_id']).update({
        'tema': datos['tema'],
        'resumen': datos['resumen'],
        'fecha_doc': datos.get('fecha_doc', '')
    })