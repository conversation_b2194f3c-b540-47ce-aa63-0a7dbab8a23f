import pixeltable as pxt
from pixeltable.functions import ollama
import json
from datetime import datetime

# Asumimos que ya existe la tabla BaseLegal creada en ingesta
tbl = pxt.get_table("redactor_legal.BaseLegal")

# Función auxiliar para enviar entry actual al agente
def formatear_prompt(texto: str):
    return f"""
Estás organizando contenido legal-laboral para crear una base de conocimiento SEO.
1) Indica el tema principal, un único tema breve (como: "vacaciones", "finiquito", etc.).
2) Proporciona un resumen de máximo 60 palabras.
3) Si encuentras una fecha en el texto (día, mes, año), indícalo en formato YYYY-MM-DD, si no, devuelve vacío.
Formato de salida JSON:
{{ "tema": "...", "resumen": "...", "fecha_doc": "YYYY-MM-DD" }}
Texto de referencia:
---
{texto}
---
"""

# Función para procesar con Ollama
@pxt.udf
def procesar_con_ollama(texto: str) -> str:
    prompt = formatear_prompt(texto)
    return ollama.chat(
        messages=[{"role": "user", "content": prompt}],
        model="llama3.1:8b"
    ).choices[0].message.content

# Agregar columna computada para el procesamiento
try:
    tbl.add_computed_column(procesamiento_ollama=procesar_con_ollama(tbl.texto_original))
except:
    pass  # La columna ya existe

# Procesar entradas sin tema ni resumen
for row in tbl.where((tbl.tema == '') | (tbl.resumen == '')).collect():
    try:
        # Obtener el resultado del procesamiento
        resultado = row.get('procesamiento_ollama', '')
        if not resultado:
            # Si no hay resultado, procesar directamente
            prompt = formatear_prompt(row['texto_original'])
            response = ollama.chat(
                messages=[{"role": "user", "content": prompt}],
                model="llama3.1:8b"
            )
            resultado = response.choices[0].message.content

        # Analizamos respuesta JSON
        datos = json.loads(resultado)
        # Guardamos datos organizados en la tabla
        tbl.where(tbl._id == row['_id']).update({
            'tema': datos['tema'],
            'resumen': datos['resumen'],
            'fecha_doc': datos.get('fecha_doc', '')
        })
        print(f"Procesado registro {row['_id']}: tema='{datos['tema']}'")
    except Exception as e:
        print(f"Error procesando registro {row['_id']}: {e}")
        continue