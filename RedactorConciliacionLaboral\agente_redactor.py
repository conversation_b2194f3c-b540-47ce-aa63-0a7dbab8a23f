import pixeltable as pxt
from pixelagent.openai import Agent
from datetime import datetime

# Obtenemos la tabla con la base legal
tbl_base = pxt.get_table("BaseLegal")
# Creamos una nueva tabla para Artículos SEO
class ArticuloSEO(pxt.Table):
    id: int = pxt.Column(primary_key=True)
    id_fuente: int          # referencia a BaseLegal.id
    tema: str
    titulo: str
    contenido_markdown: str
    fecha_creacion: datetime = pxt.Column(default_factory=datetime.utcnow)

pxt.create_table(ArticuloSEO)
tbl_art = pxt.get_table("ArticuloSEO")

# Prompt generador del artículo
def prompt_articulo(texto: str, tema: str, resumen: str):
    return f"""
Eres redactor SEO experto en derecho laboral. Basándote en este contenido:
- Tema: {tema}
- Resumen: {resumen}

Genera un **artículo completo en Español**, incluyendo:
1. <PERSON><PERSON><PERSON><PERSON> optimizado (<60 caracteres) con palabra clave, ej.: "¿Qué es el finiquito laboral?"
2. Introducción concisa.
3. Secciones con H2/H3, negritas y listas.
4. Una sección de preguntas frecuentes (FAQ).
5. Conclusión con llamado a acción (ej. “Para más guía consulta conciliacionlaboral.net”).
Escribe en **markdown**.
Contenido de referencia:
---
{texto}
---
"""

# Instanciamos el agente redactor
agent_red = Agent(
    name="redactor_seo",
    system_prompt="Eres redactor SEO de artículos legales laborales para el sitio conciliacionlaboral.net."
)

# Procesamos las entradas organizadas que aún no tienen artículo
for row in tbl_base.where(~tbl_base.id.isin(tbl_art.select(tbl_art.id_fuente))).collect():
    prompt = prompt_articulo(row['texto_original'], row['tema'], row['resumen'])
    markdown = agent_red.chat(prompt)
    # Guardamos el artículo generado
    nuevo = ArticuloSEO(
        id_fuente=row['id'],
        tema=row['tema'],
        titulo=markdown.splitlines()[0].strip("# "),  # sacamos el primer H1 como título
        contenido_markdown=markdown,
    )
    nuevo.insert()
    print(f"Artículo generado y guardado para id_fuente {row['id']}")
