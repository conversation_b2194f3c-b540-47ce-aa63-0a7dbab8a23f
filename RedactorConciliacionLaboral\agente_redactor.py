import pixeltable as pxt
from pixeltable.functions import ollama
from datetime import datetime, timezone

# Obtenemos la tabla con la base legal
tbl_base = pxt.get_table("redactor_legal.BaseLegal")

# Creamos una nueva tabla para Artículos SEO
articulo_seo_schema = {
    'id_fuente': pxt.Int,          # referencia a BaseLegal.id
    'tema': pxt.String,
    'titulo': pxt.String,
    'contenido_markdown': pxt.String,
    'fecha_creacion': pxt.Timestamp
}

tbl_art = pxt.create_table("redactor_legal.ArticuloSEO", articulo_seo_schema, if_exists='ignore')

# Prompt generador del artículo
def prompt_articulo(texto: str, tema: str, resumen: str):
    return f"""
Eres redactor SEO experto en derecho laboral. Basándote en este contenido:
- Tema: {tema}
- Resumen: {resumen}

Genera un **artículo completo en Español**, incluyendo:
1. <PERSON><PERSON><PERSON><PERSON> optimizado (<60 caracteres) con palabra clave, ej.: "¿Qué es el finiquito laboral?"
2. Introducción concisa.
3. Secciones con H2/H3, negritas y listas.
4. Una sección de preguntas frecuentes (FAQ).
5. Conclusión con llamado a acción (ej. “Para más guía consulta conciliacionlaboral.net”).
Escribe en **markdown**.
Contenido de referencia:
---
{texto}
---
"""

# Función para generar artículos con Ollama
@pxt.udf
def generar_articulo_ollama(texto: str, tema: str, resumen: str) -> str:
    prompt = prompt_articulo(texto, tema, resumen)
    response = ollama.chat(
        messages=[
            {"role": "system", "content": "Eres redactor SEO de artículos legales laborales para el sitio conciliacionlaboral.net."},
            {"role": "user", "content": prompt}
        ],
        model="llama3.1:8b"
    )
    return response.choices[0].message.content

# Procesamos las entradas organizadas que aún no tienen artículo
# Primero obtenemos los IDs que ya tienen artículos
ids_con_articulos = set(row['id_fuente'] for row in tbl_art.select(tbl_art.id_fuente).collect())

# Procesamos solo las entradas que no tienen artículo
for row in tbl_base.collect():
    if row['_id'] not in ids_con_articulos:
        # Verificar que el registro tenga tema y resumen (debe estar procesado por el organizador)
        if not row['tema'] or not row['resumen']:
            print(f"Saltando registro {row['_id']}: falta tema o resumen. Ejecute primero el agente organizador.")
            continue

        try:
            # Generar artículo con Ollama
            prompt = prompt_articulo(row['texto_original'], row['tema'], row['resumen'])
            response = ollama.chat(
                messages=[
                    {"role": "system", "content": "Eres redactor SEO de artículos legales laborales para el sitio conciliacionlaboral.net."},
                    {"role": "user", "content": prompt}
                ],
                model="llama3.1:8b"
            )
            markdown = response.choices[0].message.content

            # Extraer título del markdown
            lines = markdown.splitlines()
            titulo = "Artículo Legal"  # título por defecto
            for line in lines:
                if line.strip().startswith("# "):
                    titulo = line.strip("# ").strip()
                    break

            # Guardamos el artículo generado
            tbl_art.insert({
                'id_fuente': row['_id'],
                'tema': row['tema'],
                'titulo': titulo,
                'contenido_markdown': markdown,
                'fecha_creacion': datetime.now(timezone.utc)
            })
            print(f"Artículo generado y guardado para id_fuente {row['_id']}: '{titulo}'")
        except Exception as e:
            print(f"Error generando artículo para {row['_id']}: {e}")
            continue
