#!/usr/bin/env python3
"""
REDACTOR INTELIGENTE: Genera artículos usando la base de conocimiento
Busca información relevante en Pixeltable y crea artículos únicos y optimizados
"""

import pixeltable as pxt
from pixeltable.functions.ollama import chat, embed
from datetime import datetime, timezone
import re
from pathlib import Path
import numpy as np

def verificar_base_conocimiento():
    """Verifica que la base de conocimiento esté disponible"""
    try:
        documentos_t = pxt.get_table('conocimiento_legal.documentos')
        chunks_t = pxt.get_table('conocimiento_legal.chunks')
        
        num_docs = len(documentos_t.collect())
        num_chunks = len(chunks_t.collect())
        
        print(f"✓ Base de conocimiento encontrada:")
        print(f"  📚 {num_docs} documentos")
        print(f"  📄 {num_chunks} chunks")
        
        return documentos_t, chunks_t, num_chunks > 0
    except Exception as e:
        print(f"✗ Base de conocimiento no encontrada: {e}")
        print("Ejecuta primero: python bibliotecario_inteligente.py")
        return None, None, False

def buscar_conocimiento_relevante(tema, chunks_t, max_chunks=5):
    """Busca chunks relevantes para el tema solicitado"""
    print(f"🔍 Buscando conocimiento sobre: {tema}")
    
    # Obtener todos los chunks
    chunks_data = chunks_t.collect()
    
    if not chunks_data:
        print("✗ No hay chunks en la base de conocimiento")
        return []
    
    # Buscar por tema principal exacto
    chunks_exactos = [
        chunk for chunk in chunks_data 
        if tema.lower() in chunk['tema_principal'].lower()
    ]
    
    # Buscar por subtemas y palabras clave
    chunks_relacionados = [
        chunk for chunk in chunks_data 
        if (tema.lower() in chunk['subtemas'].lower() or 
            tema.lower() in chunk['palabras_clave'].lower() or
            tema.lower() in chunk['texto_chunk'].lower())
        and chunk not in chunks_exactos
    ]
    
    # Combinar resultados priorizando exactos
    chunks_relevantes = chunks_exactos + chunks_relacionados
    
    # Limitar número de chunks
    chunks_relevantes = chunks_relevantes[:max_chunks]
    
    print(f"✓ Encontrados {len(chunks_relevantes)} chunks relevantes")
    for i, chunk in enumerate(chunks_relevantes[:3], 1):
        print(f"  {i}. Tema: {chunk['tema_principal']} | Palabras: {chunk['palabras_clave'][:50]}...")
    
    return chunks_relevantes

def generar_articulo_seo(tema, chunks_relevantes):
    """Genera un artículo SEO usando el conocimiento relevante"""
    print(f"📝 Generando artículo sobre: {tema}")

    if not chunks_relevantes:
        print("⚠️ No hay conocimiento relevante, generando artículo básico")
        conocimiento_base = f"Información general sobre {tema} en derecho laboral."
    else:
        # Combinar conocimiento relevante
        conocimiento_base = "\n\n".join([
            f"Fuente {i+1}: {chunk['texto_chunk'][:500]}..."
            for i, chunk in enumerate(chunks_relevantes)
        ])

    # Prompt para generar artículo
    prompt_articulo = f"""Eres un experto en derecho laboral mexicano. Crea un artículo SEO completo sobre "{tema}" usando ÚNICAMENTE la información proporcionada.

CONOCIMIENTO BASE:
{conocimiento_base}

INSTRUCCIONES:
1. Crea un artículo de 400-500 palabras
2. Usa formato markdown con:
   - Título principal con #
   - 3-4 secciones con ##
   - Listas con viñetas donde sea apropiado
   - Negritas para términos importantes
3. Optimizado para SEO:
   - Título atractivo con palabra clave "{tema}"
   - Introducción que enganche
   - Contenido útil y práctico
   - Conclusión con llamado a acción
4. Enfoque en derecho laboral mexicano
5. Tono profesional pero accesible
6. Incluye aspectos legales específicos
7. NO inventes información, usa solo lo proporcionado

TEMA: {tema}"""

    try:
        print("   🤖 Generando con Ollama...")
        # Usar subprocess en lugar de la función de Pixeltable para evitar errores
        import subprocess
        cmd = ['ollama', 'run', 'llama3.1:8b', prompt_articulo]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120, encoding='utf-8', errors='replace')

        if result.returncode == 0:
            articulo_markdown = result.stdout.strip()
            print("   ✓ Artículo generado exitosamente")
            return articulo_markdown
        else:
            raise Exception(f"Error en Ollama: {result.stderr}")

    except Exception as e:
        print(f"   ✗ Error generando artículo: {e}")
        # Artículo de respaldo usando el conocimiento disponible
        if chunks_relevantes:
            contenido_base = chunks_relevantes[0]['texto_chunk'][:300]
            return f"""# {tema.title()}: Guía Completa de Derecho Laboral

## Introducción

El {tema} es un aspecto fundamental del derecho laboral mexicano que todo trabajador y empleador debe conocer.

## Marco Legal

Según la información disponible:

{contenido_base}

## Aspectos Importantes

Es crucial entender los procedimientos y requisitos legales para evitar conflictos laborales.

## Conclusión

Para más información sobre {tema} y otros temas laborales, consulta con especialistas en derecho laboral.
"""
        else:
            return f"""# {tema.title()}: Guía Completa de Derecho Laboral

## Introducción

El {tema} es un aspecto fundamental del derecho laboral mexicano que todo trabajador y empleador debe conocer.

## Marco Legal

La legislación laboral mexicana establece claramente los derechos y obligaciones relacionados con {tema}.

## Aspectos Importantes

Es crucial entender los procedimientos y requisitos legales para evitar conflictos laborales.

## Conclusión

Para más información sobre {tema} y otros temas laborales, consulta con especialistas en derecho laboral.
"""

def convertir_a_wordpress(articulo_markdown, tema):
    """Convierte el artículo a formato WordPress"""
    # Extraer título
    titulo_match = re.search(r'^#\s*(.+)$', articulo_markdown, re.MULTILINE)
    titulo = titulo_match.group(1) if titulo_match else f"Guía sobre {tema.title()}"
    titulo = re.sub(r'\*\*(.+?)\*\*', r'\1', titulo)
    
    # Convertir markdown a HTML
    html = articulo_markdown
    
    # Títulos
    html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
    html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
    html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
    
    # Negritas e itálicas
    html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
    html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)
    
    # Listas
    lineas = html.split('\n')
    html_lineas = []
    en_lista = False
    
    for linea in lineas:
        if re.match(r'^\s*[\*\-\+]\s+', linea):
            if not en_lista:
                html_lineas.append('<ul>')
                en_lista = True
            item = re.sub(r'^\s*[\*\-\+]\s+', '', linea)
            html_lineas.append(f'<li>{item}</li>')
        else:
            if en_lista:
                html_lineas.append('</ul>')
                en_lista = False
            if linea.strip() and not linea.startswith('<'):
                html_lineas.append(f'<p>{linea}</p>')
            elif linea.strip():
                html_lineas.append(linea)
    
    if en_lista:
        html_lineas.append('</ul>')
    
    articulo_html = '\n'.join(html_lineas)
    
    # Generar metadatos SEO
    slug = re.sub(r'[^\w\s-]', '', titulo.lower())
    slug = re.sub(r'[-\s]+', '-', slug).strip('-')
    
    # Extraer primer párrafo para meta description
    primer_parrafo = ""
    for linea in articulo_markdown.split('\n'):
        if linea.strip() and not linea.startswith('#') and not linea.startswith('*'):
            primer_parrafo = linea.strip()[:157] + "..."
            break
    
    meta_description = primer_parrafo if primer_parrafo else f"Guía completa sobre {tema} en derecho laboral mexicano."
    keywords = f"{tema}, derecho laboral, ley federal trabajo, méxico, laboral"
    
    return {
        'titulo': titulo,
        'slug': slug,
        'meta_description': meta_description,
        'keywords': keywords,
        'articulo_markdown': articulo_markdown,
        'articulo_html': articulo_html
    }

def exportar_articulo_wordpress(articulo_data, tema):
    """Exporta el artículo para WordPress"""
    # Crear directorio de exportación
    export_dir = Path("articulos_generados")
    export_dir.mkdir(exist_ok=True)
    
    # Crear archivo WordPress
    wordpress_content = f"""<!-- ARTÍCULO GENERADO POR REDACTOR INTELIGENTE -->
<!-- Título: {articulo_data['titulo']} -->
<!-- Slug: {articulo_data['slug']} -->
<!-- Meta Description: {articulo_data['meta_description']} -->
<!-- Keywords: {articulo_data['keywords']} -->
<!-- Categoría: Derecho Laboral -->
<!-- Tema solicitado: {tema} -->

{articulo_data['articulo_html']}

<!-- Generado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} -->
<!-- Sistema: Redactor Inteligente con Base de Conocimiento -->
"""
    
    # Guardar archivo
    nombre_archivo = f"{articulo_data['slug']}.html"
    ruta_archivo = export_dir / nombre_archivo
    
    with open(ruta_archivo, 'w', encoding='utf-8') as f:
        f.write(wordpress_content)
    
    # Guardar también el markdown
    ruta_markdown = export_dir / f"{articulo_data['slug']}.md"
    with open(ruta_markdown, 'w', encoding='utf-8') as f:
        f.write(articulo_data['articulo_markdown'])
    
    print(f"✓ Artículo exportado:")
    print(f"  📄 HTML: {ruta_archivo}")
    print(f"  📝 Markdown: {ruta_markdown}")
    
    return ruta_archivo

def generar_articulo_por_tema(tema):
    """Función principal para generar un artículo por tema"""
    print(f"=== REDACTOR INTELIGENTE: {tema.upper()} ===\n")
    
    # Verificar base de conocimiento
    documentos_t, chunks_t, tiene_datos = verificar_base_conocimiento()
    if not tiene_datos:
        return
    
    # Buscar conocimiento relevante
    chunks_relevantes = buscar_conocimiento_relevante(tema, chunks_t)
    
    # Generar artículo
    articulo_markdown = generar_articulo_seo(tema, chunks_relevantes)
    
    # Convertir a WordPress
    articulo_data = convertir_a_wordpress(articulo_markdown, tema)
    
    # Exportar
    ruta_archivo = exportar_articulo_wordpress(articulo_data, tema)
    
    # Mostrar resumen
    print(f"\n🎉 ARTÍCULO GENERADO EXITOSAMENTE:")
    print(f"   📰 Título: {articulo_data['titulo']}")
    print(f"   🔗 Slug: {articulo_data['slug']}")
    print(f"   📝 Meta: {articulo_data['meta_description'][:60]}...")
    print(f"   🏷️ Keywords: {articulo_data['keywords']}")
    print(f"   📁 Archivo: {ruta_archivo}")
    
    print(f"\n📋 PREVIEW DEL ARTÍCULO:")
    print("-" * 40)
    print(articulo_markdown[:300] + "...")
    print("-" * 40)
    
    print(f"\n✅ LISTO PARA WORDPRESS:")
    print("1. Abre el archivo .html generado")
    print("2. Copia el contenido HTML")
    print("3. Pega en WordPress")
    print("4. Configura SEO con los metadatos proporcionados")
    print("5. Publica en conciliacionlaboral.net")

def menu_redactor():
    """Menú interactivo del redactor"""
    print("=== REDACTOR INTELIGENTE ===\n")
    
    # Verificar base de conocimiento
    documentos_t, chunks_t, tiene_datos = verificar_base_conocimiento()
    if not tiene_datos:
        return
    
    # Mostrar temas disponibles
    chunks_data = chunks_t.collect()
    temas_disponibles = set()
    for chunk in chunks_data:
        temas_disponibles.add(chunk['tema_principal'])
        # Agregar subtemas
        if chunk['subtemas']:
            subtemas = [s.strip() for s in chunk['subtemas'].split(',')]
            temas_disponibles.update(subtemas)
    
    print(f"📋 TEMAS DISPONIBLES EN LA BASE DE CONOCIMIENTO:")
    for tema in sorted(temas_disponibles):
        if tema and tema != 'general':
            print(f"  • {tema}")
    
    print(f"\n💡 EJEMPLOS DE TEMAS:")
    print("  • finiquito")
    print("  • vacaciones")
    print("  • jornada laboral")
    print("  • despido")
    print("  • salario")
    
    while True:
        tema = input(f"\n🎯 ¿Sobre qué tema quieres generar un artículo? (o 'salir'): ").strip()
        
        if tema.lower() == 'salir':
            print("¡Hasta luego!")
            break
        
        if tema:
            generar_articulo_por_tema(tema)
            
            continuar = input(f"\n¿Generar otro artículo? (s/n): ").lower()
            if continuar != 's':
                break
        else:
            print("Por favor ingresa un tema válido.")

if __name__ == "__main__":
    menu_redactor()
