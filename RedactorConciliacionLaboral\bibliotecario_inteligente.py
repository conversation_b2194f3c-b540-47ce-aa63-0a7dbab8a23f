#!/usr/bin/env python3
"""
BIBLIOTECARIO INTELIGENTE: Sistema de ingesta y organización de conocimiento legal
Almacena URLs y PDFs de manera inteligente en Pixeltable, organizados por temas
"""

import pixeltable as pxt
from pixeltable.functions.ollama import chat, embed
from datetime import datetime, timezone
import trafilatura
import pdfplumber
import re
import os
from pathlib import Path

def verificar_ollama():
    """Verifica que Ollama esté funcionando"""
    try:
        import subprocess
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'llama3.1:8b' in result.stdout:
            print("✓ Ollama y modelo llama3.1:8b disponibles")
            return True
        else:
            print("✗ Modelo llama3.1:8b no encontrado")
            return False
    except Exception as e:
        print(f"✗ Error verificando Ollama: {e}")
        return False

def extraer_texto_url(url):
    """Extrae texto de una URL"""
    try:
        print(f"   📥 Descargando: {url}")
        downloaded = trafilatura.fetch_url(url)
        if downloaded:
            texto = trafilatura.extract(downloaded)
            if texto:
                print(f"   ✓ Extraído: {len(texto)} caracteres")
                return texto
        print(f"   ✗ No se pudo extraer texto")
        return None
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return None

def extraer_texto_pdf(ruta_pdf):
    """Extrae texto de un PDF"""
    try:
        print(f"   📄 Procesando PDF: {ruta_pdf}")
        texto_completo = ""
        
        with pdfplumber.open(ruta_pdf) as pdf:
            for pagina in pdf.pages:
                texto_pagina = pagina.extract_text()
                if texto_pagina:
                    texto_completo += texto_pagina + "\n"
        
        if texto_completo.strip():
            print(f"   ✓ Extraído: {len(texto_completo)} caracteres")
            return texto_completo.strip()
        else:
            print(f"   ✗ No se pudo extraer texto")
            return None
    except Exception as e:
        print(f"   ✗ Error: {e}")
        return None

def chunquear_texto(texto, max_chars=1000):
    """Divide el texto en chunks inteligentes por párrafos"""
    # Dividir por párrafos
    paragrafos = texto.split('\n\n')
    chunks = []
    chunk_actual = ""
    
    for paragrafo in paragrafos:
        paragrafo = paragrafo.strip()
        if not paragrafo:
            continue
            
        # Si agregar este párrafo excede el límite, guardar chunk actual
        if len(chunk_actual) + len(paragrafo) > max_chars and chunk_actual:
            chunks.append(chunk_actual.strip())
            chunk_actual = paragrafo
        else:
            if chunk_actual:
                chunk_actual += "\n\n" + paragrafo
            else:
                chunk_actual = paragrafo
    
    # Agregar último chunk
    if chunk_actual:
        chunks.append(chunk_actual.strip())
    
    return chunks

def configurar_base_conocimiento():
    """Configura la base de conocimiento en Pixeltable"""
    print("📚 Configurando base de conocimiento...")
    
    # Limpiar y crear directorio
    try:
        pxt.drop_dir('conocimiento_legal', force=True)
    except:
        pass
    
    pxt.create_dir('conocimiento_legal')
    
    # Tabla para documentos originales
    documentos_schema = {
        'fuente': pxt.String,
        'tipo_fuente': pxt.String,  # 'URL' o 'PDF'
        'titulo': pxt.String,
        'texto_completo': pxt.String,
        'fecha_ingesta': pxt.Timestamp
    }
    
    documentos_t = pxt.create_table('conocimiento_legal.documentos', documentos_schema)
    
    # Tabla para chunks de conocimiento
    chunks_schema = {
        'documento_id': pxt.Int,  # referencia a documentos
        'chunk_numero': pxt.Int,
        'texto_chunk': pxt.String,
        'tema_principal': pxt.String,
        'subtemas': pxt.String,
        'palabras_clave': pxt.String
    }
    
    chunks_t = pxt.create_table('conocimiento_legal.chunks', chunks_schema)
    
    print("✓ Base de conocimiento configurada")
    return documentos_t, chunks_t

def procesar_documento(fuente, tipo_fuente, texto, documentos_t, chunks_t):
    """Procesa un documento completo y lo almacena inteligentemente"""
    print(f"\n📖 Procesando documento: {fuente[:50]}...")
    
    # Extraer título del texto
    lineas = texto.split('\n')
    titulo = lineas[0][:100] if lineas else fuente
    
    # Insertar documento original
    doc_data = {
        'fuente': fuente,
        'tipo_fuente': tipo_fuente,
        'titulo': titulo,
        'texto_completo': texto,
        'fecha_ingesta': datetime.now(timezone.utc)
    }
    
    documentos_t.insert([doc_data])
    
    # Obtener ID del documento insertado
    doc_id = len(documentos_t.collect())  # Simple counter para este demo
    
    # Chunquear el texto
    chunks = chunquear_texto(texto)
    print(f"   📄 Dividido en {len(chunks)} chunks")
    
    # Procesar cada chunk
    chunks_data = []
    for i, chunk in enumerate(chunks):
        print(f"   🔍 Analizando chunk {i+1}/{len(chunks)}...")
        
        # Analizar chunk con Ollama para extraer metadatos
        prompt_analisis = f"""Analiza este fragmento de texto legal y extrae:

TEMA_PRINCIPAL: [tema principal en una palabra: finiquito, vacaciones, jornada, etc.]
SUBTEMAS: [subtemas separados por comas]
PALABRAS_CLAVE: [palabras clave importantes separadas por comas]

Texto:
{chunk[:500]}"""

        try:
            # Usar subprocess para llamar Ollama directamente
            import subprocess
            cmd = ['ollama', 'run', 'llama3.1:8b', prompt_analisis]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, encoding='utf-8', errors='replace')

            if result.returncode == 0:
                analisis = result.stdout.strip()
            else:
                analisis = ""

            # Extraer metadatos
            tema_principal = "general"
            subtemas = ""
            palabras_clave = ""

            if "TEMA_PRINCIPAL:" in analisis:
                tema_match = re.search(r'TEMA_PRINCIPAL:\s*([^\n]+)', analisis)
                if tema_match:
                    tema_principal = tema_match.group(1).strip().lower()

            if "SUBTEMAS:" in analisis:
                subtemas_match = re.search(r'SUBTEMAS:\s*([^\n]+)', analisis)
                if subtemas_match:
                    subtemas = subtemas_match.group(1).strip()

            if "PALABRAS_CLAVE:" in analisis:
                palabras_match = re.search(r'PALABRAS_CLAVE:\s*([^\n]+)', analisis)
                if palabras_match:
                    palabras_clave = palabras_match.group(1).strip()

        except Exception as e:
            print(f"     ⚠️ Error en análisis: {e}")
            tema_principal = "general"
            subtemas = ""
            palabras_clave = ""

        # Análisis simple basado en palabras clave si Ollama falla
        if tema_principal == "general":
            chunk_lower = chunk.lower()
            if "plataforma" in chunk_lower or "digital" in chunk_lower:
                tema_principal = "plataformas_digitales"
            elif "finiquito" in chunk_lower:
                tema_principal = "finiquito"
            elif "vacaciones" in chunk_lower:
                tema_principal = "vacaciones"
            elif "despido" in chunk_lower or "rescisión" in chunk_lower:
                tema_principal = "despido"
            elif "salario" in chunk_lower or "remuneración" in chunk_lower:
                tema_principal = "salario"

        # No usar embeddings por ahora para evitar errores
        embedding = [0.0] * 384  # embedding dummy
        
        chunk_data = {
            'documento_id': doc_id,
            'chunk_numero': i + 1,
            'texto_chunk': chunk,
            'tema_principal': tema_principal,
            'subtemas': subtemas,
            'palabras_clave': palabras_clave
        }
        
        chunks_data.append(chunk_data)
        print(f"     ✓ Tema: {tema_principal}")
    
    # Insertar todos los chunks
    if chunks_data:
        chunks_t.insert(chunks_data)
        print(f"   ✓ {len(chunks_data)} chunks almacenados en base de conocimiento")
    
    return doc_id, len(chunks_data)

def ingestar_fuentes(urls=None, pdfs=None):
    """Función principal para ingestar múltiples fuentes"""
    print("=== BIBLIOTECARIO INTELIGENTE: INGESTA DE CONOCIMIENTO ===\n")
    
    if not verificar_ollama():
        return
    
    # Configurar base de conocimiento
    documentos_t, chunks_t = configurar_base_conocimiento()
    
    total_docs = 0
    total_chunks = 0
    
    # Procesar URLs
    if urls:
        print(f"\n📥 PROCESANDO {len(urls)} URLs...")
        for i, url in enumerate(urls, 1):
            print(f"\n--- URL {i}/{len(urls)} ---")
            texto = extraer_texto_url(url)
            if texto:
                doc_id, num_chunks = procesar_documento(url, 'URL', texto, documentos_t, chunks_t)
                total_docs += 1
                total_chunks += num_chunks
    
    # Procesar PDFs
    if pdfs:
        print(f"\n📄 PROCESANDO {len(pdfs)} PDFs...")
        for i, pdf_path in enumerate(pdfs, 1):
            print(f"\n--- PDF {i}/{len(pdfs)} ---")
            if os.path.exists(pdf_path):
                texto = extraer_texto_pdf(pdf_path)
                if texto:
                    nombre_archivo = Path(pdf_path).name
                    doc_id, num_chunks = procesar_documento(nombre_archivo, 'PDF', texto, documentos_t, chunks_t)
                    total_docs += 1
                    total_chunks += num_chunks
            else:
                print(f"   ✗ Archivo no encontrado: {pdf_path}")
    
    # Mostrar estadísticas finales
    print(f"\n📊 INGESTA COMPLETADA:")
    print(f"   📚 Documentos procesados: {total_docs}")
    print(f"   📄 Chunks almacenados: {total_chunks}")
    print(f"   🗂️ Base de conocimiento: conocimiento_legal")
    
    # Mostrar resumen por temas
    if total_chunks > 0:
        print(f"\n📋 RESUMEN POR TEMAS:")
        chunks_data = chunks_t.collect()
        temas = {}
        for chunk in chunks_data:
            tema = chunk['tema_principal']
            temas[tema] = temas.get(tema, 0) + 1
        
        for tema, cantidad in sorted(temas.items()):
            print(f"   📌 {tema}: {cantidad} chunks")
    
    print(f"\n✅ BASE DE CONOCIMIENTO LISTA PARA EL REDACTOR")
    return total_docs, total_chunks

def demo_bibliotecario():
    """Demo del bibliotecario con contenido de ejemplo"""
    print("=== DEMO: BIBLIOTECARIO INTELIGENTE ===\n")
    
    # URLs de ejemplo sobre derecho laboral mexicano
    urls_ejemplo = [
        "https://www.gob.mx/stps/documentos/reforma-en-materia-de-trabajo-en-plataformas-digitales",
        "https://www.profedet.gob.mx/micrositio/index.php/salario",
        "https://www.profedet.gob.mx/micrositio/index.php/vacaciones",
        "https://www.profedet.gob.mx/micrositio/index.php/aguinaldo",
        "https://www.profedet.gob.mx/micrositio/index.php/jornada-laboral",
        "https://www.profedet.gob.mx/micrositio/index.php/reparto-de-utilidades-o-participacion-de-los-trabajadores-en-las-utilidades",
        "https://www.profedet.gob.mx/micrositio/index.php/prima-dominical",
        "https://www.profedet.gob.mx/micrositio/index.php/prima-de-atiguedad",
        "https://www.profedet.gob.mx/micrositio/index.php/horas-extras-u-horas-de-trabajo-extraordinario",
        "https://www.profedet.gob.mx/micrositio/index.php/prima-vacacional",
        "https://www.profedet.gob.mx/micrositio/index.php/dias-de-descanso",
        "https://www.profedet.gob.mx/micrositio/index.php/derecho-de-preferencia",
        "https://www.profedet.gob.mx/micrositio/index.php/despido",
        "https://www.profedet.gob.mx/micrositio/index.php/rescision",
        "https://www.profedet.gob.mx/micrositio/index.php/renuncia-voluntaria",
        "https://www.profedet.gob.mx/micrositio/index.php/descuentos-indebidos",
        "https://www.profedet.gob.mx/micrositio/index.php/seguridad-social-trabajadora",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-trabajadora",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-de-cesantia",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-por-vejez",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-de-invalidez",
        "https://www.profedet.gob.mx/micrositio/index.php/pago-de-incapacidades",
        "https://www.profedet.gob.mx/micrositio/index.php/riesgo-de-trabajo",
        "https://www.profedet.gob.mx/micrositio/index.php/liberacion-de-credito-persona-trabajadora",
        "https://www.profedet.gob.mx/micrositio/index.php/devolucion-del-total-de-los-recursos-de-la-cuenta-de-afore-trabajadora",
        "https://www.profedet.gob.mx/micrositio/index.php/devolucion-de-la-subcuenta-de-vivienda-trabadora",
        "https://www.profedet.gob.mx/micrositio/index.php/devolucion-de-la-subcuenta-de-retiro-trabajadora",
        "https://www.profedet.gob.mx/micrositio/index.php/retiro-por-matrimonio",
        "https://www.profedet.gob.mx/micrositio/index.php/retiro-por-desempleo",
        "https://www.profedet.gob.mx/micrositio/index.php/registro-de-beneficiarios-en-la-afore-ante-la-afore",
        "https://www.profedet.gob.mx/micrositio/index.php/pago-de-finiquito",
        "https://www.profedet.gob.mx/micrositio/index.php/indemnizacion-en-caso-de-muerte-por-trabajo-sin-seguro-social",
        "https://www.profedet.gob.mx/micrositio/index.php/seguridad-social-beneficiaria",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-de-viudez",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-de-orfandad",
        "https://www.profedet.gob.mx/micrositio/index.php/pension-de-ascendencia",
        "https://www.profedet.gob.mx/micrositio/index.php/liberacion-de-credito-por-fallecimiento",
        "https://www.profedet.gob.mx/micrositio/index.php/devolucion-del-total-de-la-afore-de-los-recursos-de-la-cuenta-de-afore-beneficiaria",
        "https://www.profedet.gob.mx/micrositio/index.php/devolucion-de-la-subcuenta-de-vivienda-beneficiaria",
        "https://www.profedet.gob.mx/micrositio/index.php/devolucion-de-la-subcuenta-de-retiro-beneficiaria",
        "https://www.profedet.gob.mx/micrositio/index.php/aportaciones-voluntarias",
        "https://www.profedet.gob.mx/micrositio/index.php/derecho-colectivo",
        "https://www.profedet.gob.mx/micrositio/index.php/libertad-sindical",
        "https://www.profedet.gob.mx/micrositio/index.php/democracia-sindical",
        "https://www.profedet.gob.mx/micrositio/index.php/negociacion-colectiva-autentica",
        "https://www.profedet.gob.mx/micrositio/index.php/conformacion-de-un-sindicato",
        "https://www.profedet.gob.mx/micrositio/index.php/contrato-colectivo-de-trabajo",
        "https://www.profedet.gob.mx/micrositio/index.php/huelga",
        "https://www.profedet.gob.mx/profedet/transparencia/focalizada/conoce_prestaciones_labores.html",
        "https://reformalaboral.stps.gob.mx/sindicatos",
        "https://www.gob.mx/cfcrl/articulos/conccd iliacion-laboral"
    ]
    
    # PDFs de ejemplo (agregar rutas reales si tienes)
    pdfs_ejemplo = [
        "documentos/Preguntas_Frecuentes_Plataformas_Digitales.pdf",
        "documentos/LFT.pdf",
        "documentos/LSS.pdf"
    ]
    
    print("📋 FUENTES CONFIGURADAS:")
    print("URLs:")
    for url in urls_ejemplo:
        print(f"  - {url}")
    
    if pdfs_ejemplo:
        print("PDFs:")
        for pdf in pdfs_ejemplo:
            print(f"  - {pdf}")
    
    respuesta = input(f"\n¿Procesar estas fuentes? (s/n): ").lower()
    if respuesta == 's':
        total_docs, total_chunks = ingestar_fuentes(urls_ejemplo, pdfs_ejemplo)
        
        if total_chunks > 0:
            print(f"\n🎉 ¡BIBLIOTECARIO CONFIGURADO EXITOSAMENTE!")
            print("Ahora puedes usar el redactor para generar artículos.")
            print("Ejemplo: python redactor_inteligente.py")
    else:
        print("Operación cancelada")

if __name__ == "__main__":
    demo_bibliotecario()
