import os
import trafilatura
import pdfplumber
import pixeltable as pt
from datetime import datetime

# Define la tabla en Pixeltable
class BaseLegal(pt.Table):
    id: int = pt.Column(primary_key=True)
    fuente: str
    tipo_fuente: str
    texto_original: str
    tema: str
    resumen: str
    fecha_doc: str
    fecha_ingesta: datetime = pt.Column(default_factory=datetime.utcnow)

# Crear la tabla si no existe
pt.create_table(BaseLegal)

# Extraer desde URL con Trafilatura
def extraer_desde_url(url: str) -> str:
    downloaded = trafilatura.fetch_url(url)
    if downloaded:
        return trafilatura.extract(downloaded)
    return ""

# Extraer desde PDF con pdfplumber
def extraer_desde_pdf(path_pdf: str) -> str:
    texto = ""
    with pdfplumber.open(path_pdf) as pdf:
        for page in pdf.pages:
            texto += page.extract_text() + "\n"
    return texto

# Guardar en Pixeltable
def almacenar_en_pixeltable(fuente: str, tipo: str, texto: str, tema: str = "por_clasificar", resumen: str = "", fecha_doc: str = ""):
    entrada = BaseLegal(
        fuente=fuente,
        tipo_fuente=tipo,
        texto_original=texto,
        tema=tema,
        resumen=resumen,
        fecha_doc=fecha_doc,
    )
    entrada.insert()

# Ejemplo de uso
if __name__ == "__main__":
    # URL de ejemplo
    url = "https://www.gob.mx/profedet/articulos/que-es-la-jornada-de-trabajo"
    texto_url = extraer_desde_url(url)
    if texto_url:
        almacenar_en_pixeltable(fuente=url, tipo="url", texto=texto_url)

    # PDF local de ejemplo
    path_pdf = "ejemplo_finiquito.pdf"
    if os.path.exists(path_pdf):
        texto_pdf = extraer_desde_pdf(path_pdf)
        almacenar_en_pixeltable(fuente=path_pdf, tipo="pdf", texto=texto_pdf)
