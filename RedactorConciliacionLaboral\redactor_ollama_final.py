#!/usr/bin/env python3
"""
Sistema de Redactor de Conciliación Laboral con Ollama - Versión Final
Funciona sin columnas computadas, procesando directamente
"""

import pixeltable as pxt
from pixeltable.functions import ollama
from datetime import datetime, timezone
import json
import re

def verificar_ollama():
    """Verifica que Ollama esté funcionando"""
    try:
        # Test directo con Ollama
        import subprocess
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'llama3.1:8b' in result.stdout:
            print("✓ Ollama y modelo llama3.1:8b disponibles")
            return True
        else:
            print("✗ Modelo llama3.1:8b no encontrado")
            print("Ejecuta: ollama pull llama3.1:8b")
            return False
    except Exception as e:
        print(f"✗ Error verificando Ollama: {e}")
        return False

def procesar_con_ollama_directo(texto, tipo="organizar"):
    """Procesa texto directamente con Ollama usando subprocess"""
    try:
        import subprocess
        
        if tipo == "organizar":
            prompt = f"""Analiza este texto legal y extrae:
1. Tema principal (una palabra): finiquito, vacaciones, jornada, etc.
2. Resumen breve (máximo 50 palabras)

Texto: {texto}

Responde en formato:
TEMA: [palabra]
RESUMEN: [resumen breve]"""
        
        else:  # generar artículo
            prompt = f"""Crea un artículo SEO en markdown sobre este tema legal:

{texto}

Incluye:
- Título con # 
- Introducción
- 2-3 secciones con ##
- Conclusión

Escribe en español, máximo 300 palabras."""
        
        # Ejecutar Ollama directamente
        cmd = ['ollama', 'run', 'llama3.1:8b', prompt]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            return f"Error: {result.stderr}"
            
    except Exception as e:
        return f"Error procesando: {str(e)}"

def main():
    print("=== REDACTOR DE CONCILIACIÓN LABORAL CON OLLAMA ===\n")
    
    # 1. Verificar Ollama
    if not verificar_ollama():
        return
    
    # 2. Configurar base de datos
    print("1. Configurando base de datos...")
    pxt.create_dir("redactor_legal", if_exists='ignore')
    
    base_legal_schema = {
        'fuente': pxt.String,
        'tipo_fuente': pxt.String,
        'texto_original': pxt.String,
        'tema': pxt.String,
        'resumen': pxt.String,
        'fecha_doc': pxt.String,
        'fecha_ingesta': pxt.Timestamp
    }
    
    articulo_seo_schema = {
        'id_fuente': pxt.Int,
        'tema': pxt.String,
        'titulo': pxt.String,
        'contenido_markdown': pxt.String,
        'fecha_creacion': pxt.Timestamp
    }
    
    base_legal_t = pxt.create_table("redactor_legal.BaseLegal", base_legal_schema, if_exists='replace')
    articulo_seo_t = pxt.create_table("redactor_legal.ArticuloSEO", articulo_seo_schema, if_exists='replace')
    
    print("✓ Tablas creadas\n")
    
    # 3. Ingresar datos de prueba
    print("2. Ingresando datos de prueba...")
    
    datos_prueba = [
        {
            'fuente': 'Código del Trabajo - Art. 159',
            'tipo_fuente': 'ley',
            'texto_original': 'El finiquito es el documento que acredita el término de la relación laboral y la liquidación de las prestaciones que correspondan al trabajador. Debe contener la individualización de las partes, la causa del término del contrato, el detalle de las prestaciones pagadas y la firma de ambas partes.',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo - Art. 25',
            'tipo_fuente': 'ley',
            'texto_original': 'Las vacaciones anuales son un derecho irrenunciable del trabajador. Todo trabajador con más de un año de servicios tendrá derecho a un feriado anual de quince días hábiles, con remuneración íntegra que se pagará antes del inicio del período de vacaciones.',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        }
    ]
    
    base_legal_t.insert(datos_prueba)
    print(f"✓ {len(datos_prueba)} registros ingresados\n")
    
    # 4. Organizar contenido con Ollama
    print("3. Organizando contenido con Ollama...")
    
    registros = base_legal_t.collect()
    for i, row in enumerate(registros):
        if not row['tema']:
            print(f"   Procesando registro {i+1}...")

            # Procesar con Ollama
            resultado = procesar_con_ollama_directo(row['texto_original'], "organizar")
            print(f"   Respuesta: {resultado[:100]}...")

            # Extraer tema y resumen
            tema = "derecho_laboral"  # valor por defecto
            resumen = row['texto_original'][:50] + "..."  # valor por defecto

            if "TEMA:" in resultado:
                try:
                    tema_match = re.search(r'TEMA:\s*([^\n]+)', resultado)
                    if tema_match:
                        tema = tema_match.group(1).strip().replace('*', '')

                    resumen_match = re.search(r'RESUMEN:\s*([^\n]+)', resultado)
                    if resumen_match:
                        resumen = resumen_match.group(1).strip().replace('*', '')
                except:
                    pass

            # Actualizar registro
            base_legal_t.where(base_legal_t._id == row['_id']).update({
                'tema': tema,
                'resumen': resumen
            })

            print(f"   ✓ Organizado: tema='{tema}'\n")
    
    # 5. Generar artículos SEO
    print("4. Generando artículos SEO con Ollama...")
    
    registros_organizados = base_legal_t.where(base_legal_t.tema != '').collect()
    
    for i, row in enumerate(registros_organizados):
        print(f"   Generando artículo {i+1} sobre '{row['tema']}'...")
        
        # Crear prompt para artículo
        contexto = f"Tema: {row['tema']}\nResumen: {row['resumen']}\nTexto original: {row['texto_original']}"
        
        # Generar artículo con Ollama
        markdown = procesar_con_ollama_directo(contexto, "articulo")
        
        # Extraer título
        titulo_match = re.search(r'^#\s*(.+)$', markdown, re.MULTILINE)
        titulo = titulo_match.group(1) if titulo_match else f"Guía sobre {row['tema']}"
        
        # Guardar artículo
        articulo_seo_t.insert({
            'id_fuente': row['_id'],
            'tema': row['tema'],
            'titulo': titulo,
            'contenido_markdown': markdown,
            'fecha_creacion': datetime.now(timezone.utc)
        })
        
        print(f"   ✓ Artículo generado: '{titulo}'\n")
    
    # 6. Mostrar resultados
    print("5. Resultados finales:")
    
    print("\n--- Base Legal Organizada ---")
    registros_finales = base_legal_t.select(base_legal_t.fuente, base_legal_t.tema, base_legal_t.resumen).collect()
    for i, row in enumerate(registros_finales, 1):
        print(f"{i}. {row['fuente']}")
        print(f"   Tema: {row['tema']}")
        print(f"   Resumen: {row['resumen']}")
        print()
    
    print("--- Artículos SEO Generados ---")
    articulos = articulo_seo_t.select(articulo_seo_t.titulo, articulo_seo_t.tema).collect()
    for i, articulo in enumerate(articulos, 1):
        print(f"{i}. {articulo['titulo']}")
        print(f"   Tema: {articulo['tema']}")
        print()
    
    # Mostrar ejemplo de artículo
    if articulos:
        print("--- Ejemplo de Artículo Generado ---")
        primer_articulo = articulo_seo_t.select(articulo_seo_t.contenido_markdown).limit(1).collect()[0]
        print(primer_articulo['contenido_markdown'][:500] + "...")
    
    print("\n=== SISTEMA FUNCIONANDO CORRECTAMENTE CON OLLAMA ===")
    print("✓ Ingesta de contenido legal: OK")
    print("✓ Organización con IA local: OK") 
    print("✓ Generación de artículos SEO: OK")
    print("\nEl sistema está listo para procesar contenido legal real.")

if __name__ == "__main__":
    main()
