#!/usr/bin/env python3
"""
SISTEMA COMPLETO: Redactor de Conciliación Laboral con Ollama
Versión final usando las mejores prácticas de Pixeltable + Ollama
"""

import pixeltable as pxt
from pixeltable.functions.ollama import chat
from datetime import datetime, timezone
import subprocess
import re

def verificar_ollama():
    """Verifica que Ollama esté funcionando"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if 'llama3.1:8b' in result.stdout:
            print("✓ Ollama y modelo llama3.1:8b disponibles")
            return True
        else:
            print("✗ Modelo llama3.1:8b no encontrado")
            print("Ejecuta: ollama pull llama3.1:8b")
            return False
    except Exception as e:
        print(f"✗ Error verificando Ollama: {e}")
        return False

def main():
    print("=== SISTEMA COMPLETO: REDACTOR CON OLLAMA ===\n")
    
    # 1. Verificar Ollama
    if not verificar_ollama():
        return
    
    # 2. Configurar base de datos
    print("1. Configurando base de datos...")
    pxt.drop_dir('redactor_ollama', force=True)
    pxt.create_dir('redactor_ollama')
    
    # Crear tabla base para contenido legal
    base_legal = pxt.create_table('redactor_ollama.base_legal', {
        'fuente': pxt.String,
        'texto_original': pxt.String,
        'fecha_ingesta': pxt.Timestamp
    })
    
    print("✓ Tabla base creada\n")
    
    # 3. Insertar datos de prueba
    print("2. Ingresando contenido legal...")
    
    contenido_legal = [
        {
            'fuente': 'Código del Trabajo Art. 159 - Finiquito',
            'texto_original': 'El finiquito es el documento que acredita el término de la relación laboral y la liquidación de las prestaciones que correspondan al trabajador. Debe contener la individualización de las partes, la causa del término del contrato, el detalle de las prestaciones pagadas y la firma de ambas partes.',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo Art. 25 - Vacaciones',
            'texto_original': 'Las vacaciones anuales son un derecho irrenunciable del trabajador. Todo trabajador con más de un año de servicios tendrá derecho a un feriado anual de quince días hábiles, con remuneración íntegra que se pagará antes del inicio del período de vacaciones.',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo Art. 22 - Jornada Laboral',
            'texto_original': 'La jornada ordinaria de trabajo no podrá exceder de cuarenta y cinco horas semanales. Podrá distribuirse en no más de seis ni en menos de cinco días. En ningún caso la jornada ordinaria podrá exceder de diez horas diarias.',
            'fecha_ingesta': datetime.now(timezone.utc)
        }
    ]
    
    base_legal.insert(contenido_legal)
    print(f"✓ {len(contenido_legal)} textos legales ingresados\n")
    
    # 4. Agregar columnas computadas con Ollama
    print("3. Configurando procesamiento con Ollama...")
    
    # Columna para extraer tema
    prompt_tema = "Analiza este texto legal y responde SOLO con una palabra que represente el tema principal (ejemplo: finiquito, vacaciones, jornada): " + base_legal.texto_original
    
    base_legal.add_computed_column(
        tema_extraido=chat(
            messages=[{'role': 'user', 'content': prompt_tema}],
            model='llama3.1:8b',
            options={'temperature': 0.3, 'max_tokens': 10}
        ).message.content
    )
    
    # Columna para generar resumen
    prompt_resumen = "Resume este texto legal en máximo 40 palabras: " + base_legal.texto_original
    
    base_legal.add_computed_column(
        resumen_extraido=chat(
            messages=[{'role': 'user', 'content': prompt_resumen}],
            model='llama3.1:8b',
            options={'temperature': 0.5, 'max_tokens': 50}
        ).message.content
    )
    
    print("✓ Columnas de organización agregadas")
    
    # Función UDF para crear prompt de artículo
    @pxt.udf
    def crear_prompt_articulo(tema: str, resumen: str, texto: str) -> str:
        return f"""Crea un artículo SEO en markdown sobre este tema legal:

Tema: {tema}
Resumen: {resumen}
Texto original: {texto}

Incluye:
- Título con #
- Introducción breve
- 2 secciones con ##
- Conclusión con llamado a acción

Máximo 300 palabras, en español."""

    # Agregar columna para el prompt del artículo
    base_legal.add_computed_column(
        prompt_articulo=crear_prompt_articulo(
            base_legal.tema_extraido,
            base_legal.resumen_extraido,
            base_legal.texto_original
        )
    )

    # Columna para generar artículo SEO
    base_legal.add_computed_column(
        articulo_seo=chat(
            messages=[
                {'role': 'system', 'content': 'Eres un redactor SEO experto en derecho laboral para el sitio conciliacionlaboral.net'},
                {'role': 'user', 'content': base_legal.prompt_articulo}
            ],
            model='llama3.1:8b',
            options={'temperature': 0.7, 'max_tokens': 400}
        ).message.content
    )
    
    print("✓ Columna de generación de artículos agregada\n")
    
    # 5. Procesar y mostrar resultados
    print("4. Procesando contenido con Ollama...")
    print("   (Esto puede tomar unos minutos...)\n")
    
    # Obtener resultados procesados
    resultados = base_legal.select(
        base_legal.fuente,
        base_legal.tema_extraido,
        base_legal.resumen_extraido,
        base_legal.articulo_seo
    ).collect()
    
    # 6. Mostrar resultados
    print("5. RESULTADOS DEL PROCESAMIENTO:")
    print("=" * 60)
    
    for i, resultado in enumerate(resultados, 1):
        print(f"\n{i}. {resultado['fuente']}")
        print(f"   📋 Tema extraído: {resultado['tema_extraido']}")
        print(f"   📝 Resumen: {resultado['resumen_extraido'][:80]}...")
        
        # Extraer título del artículo
        articulo = resultado['articulo_seo']
        titulo_match = re.search(r'^#\s*(.+)$', articulo, re.MULTILINE)
        titulo = titulo_match.group(1) if titulo_match else "Artículo generado"
        
        print(f"   📰 Artículo: {titulo}")
        print(f"   📊 Tamaño: {len(articulo)} caracteres")
    
    # 7. Mostrar ejemplo completo
    if resultados:
        print(f"\n6. EJEMPLO DE ARTÍCULO COMPLETO:")
        print("=" * 60)
        ejemplo = resultados[0]
        print(f"Fuente: {ejemplo['fuente']}")
        print(f"Tema: {ejemplo['tema_extraido']}")
        print(f"Resumen: {ejemplo['resumen_extraido']}")
        print("\nArtículo SEO generado:")
        print("-" * 40)
        print(ejemplo['articulo_seo'])
        print("-" * 40)
    
    # 8. Crear tabla final con resultados limpios
    print(f"\n7. Creando tabla final de resultados...")
    
    # Crear tabla de artículos finales
    articulos_finales = pxt.create_table('redactor_ollama.articulos_seo', {
        'fuente_original': pxt.String,
        'tema': pxt.String,
        'titulo': pxt.String,
        'resumen': pxt.String,
        'contenido_markdown': pxt.String,
        'fecha_generacion': pxt.Timestamp
    })
    
    # Insertar resultados procesados
    articulos_procesados = []
    for resultado in resultados:
        # Extraer título del markdown
        articulo = resultado['articulo_seo']
        titulo_match = re.search(r'^#\s*(.+)$', articulo, re.MULTILINE)
        titulo = titulo_match.group(1) if titulo_match else f"Guía sobre {resultado['tema_extraido']}"
        
        articulos_procesados.append({
            'fuente_original': resultado['fuente'],
            'tema': resultado['tema_extraido'],
            'titulo': titulo,
            'resumen': resultado['resumen_extraido'],
            'contenido_markdown': articulo,
            'fecha_generacion': datetime.now(timezone.utc)
        })
    
    articulos_finales.insert(articulos_procesados)
    
    print(f"✓ {len(articulos_procesados)} artículos guardados en tabla final")
    
    # 9. Estadísticas finales
    print(f"\n8. ESTADÍSTICAS FINALES:")
    print("=" * 60)
    print(f"✅ Textos legales procesados: {len(contenido_legal)}")
    print(f"✅ Temas extraídos automáticamente: {len(resultados)}")
    print(f"✅ Resúmenes generados: {len(resultados)}")
    print(f"✅ Artículos SEO creados: {len(articulos_procesados)}")
    print(f"✅ Modelo usado: llama3.1:8b (local)")
    print(f"✅ Base de datos: Pixeltable")
    
    print(f"\n🎉 ¡SISTEMA COMPLETAMENTE FUNCIONAL! 🎉")
    print("El sistema de Redactor de Conciliación Laboral está operativo.")
    print("Características implementadas:")
    print("  • Ingesta automática de contenido legal")
    print("  • Extracción de temas con IA local")
    print("  • Generación de resúmenes inteligentes")
    print("  • Creación de artículos SEO optimizados")
    print("  • Almacenamiento estructurado en Pixeltable")
    print("  • Procesamiento 100% local con Ollama")
    
    print(f"\n📋 PRÓXIMOS PASOS:")
    print("  1. Agregar más contenido legal a la tabla base")
    print("  2. Personalizar prompts para diferentes tipos de documentos")
    print("  3. Implementar validación de calidad de artículos")
    print("  4. Crear interfaz web para gestión de contenido")
    
    print(f"\n🔍 CONSULTAR RESULTADOS:")
    print("  • Tabla base: redactor_ollama.base_legal")
    print("  • Artículos finales: redactor_ollama.articulos_seo")
    print("  • Usar: pxt.get_table('redactor_ollama.articulos_seo').collect()")

if __name__ == "__main__":
    main()
