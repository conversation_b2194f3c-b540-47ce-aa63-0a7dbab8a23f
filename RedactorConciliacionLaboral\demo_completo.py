#!/usr/bin/env python3
"""
Demo completo del sistema de Redactor de Conciliación Laboral
Este script demuestra el flujo completo:
1. Ingesta de contenido legal
2. Organización y clasificación
3. Generación de artículos SEO
"""

import pixeltable as pxt
from datetime import datetime, timezone

def demo_completo():
    print("=== DEMO COMPLETO: REDACTOR DE CONCILIACIÓN LABORAL ===\n")
    
    # 1. CONFIGURACIÓN INICIAL
    print("1. Configurando base de datos...")
    pxt.create_dir("redactor_legal", if_exists='ignore')
    
    # Esquema de la tabla base legal
    base_legal_schema = {
        'fuente': pxt.String,
        'tipo_fuente': pxt.String,
        'texto_original': pxt.String,
        'tema': pxt.String,
        'resumen': pxt.String,
        'fecha_doc': pxt.String,
        'fecha_ingesta': pxt.Timestamp
    }
    
    # Esquema de artículos SEO
    articulo_seo_schema = {
        'id_fuente': pxt.Int,
        'tema': pxt.String,
        'titulo': pxt.String,
        'contenido_markdown': pxt.String,
        'fecha_creacion': pxt.Timestamp
    }
    
    # Crear tablas
    base_legal_t = pxt.create_table("redactor_legal.BaseLegal", base_legal_schema, if_exists='replace')
    articulo_seo_t = pxt.create_table("redactor_legal.ArticuloSEO", articulo_seo_schema, if_exists='replace')
    
    print("✓ Tablas creadas correctamente\n")
    
    # 2. INGESTA DE DATOS DE PRUEBA
    print("2. Ingresando datos de prueba...")
    
    # Datos de ejemplo sobre derecho laboral
    datos_prueba = [
        {
            'fuente': 'Código del Trabajo - Art. 159',
            'tipo_fuente': 'ley',
            'texto_original': '''El finiquito es el documento que acredita el término de la relación laboral y la liquidación de las prestaciones que correspondan al trabajador. Debe contener la individualización de las partes, la causa del término del contrato, el detalle de las prestaciones pagadas y la firma de ambas partes.''',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo - Art. 25',
            'tipo_fuente': 'ley',
            'texto_original': '''Las vacaciones anuales son un derecho irrenunciable del trabajador. Todo trabajador con más de un año de servicios tendrá derecho a un feriado anual de quince días hábiles, con remuneración íntegra que se pagará antes del inicio del período de vacaciones.''',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo - Art. 22',
            'tipo_fuente': 'ley',
            'texto_original': '''La jornada ordinaria de trabajo no podrá exceder de cuarenta y cinco horas semanales. Podrá distribuirse en no más de seis ni en menos de cinco días. En ningún caso la jornada ordinaria podrá exceder de diez horas diarias.''',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        }
    ]
    
    # Insertar datos
    base_legal_t.insert(datos_prueba)
    
    print(f"✓ {len(datos_prueba)} registros ingresados\n")
    
    # 3. VERIFICAR DATOS
    print("3. Verificando datos ingresados...")
    registros = base_legal_t.collect()
    print(f"✓ Total de registros en base legal: {len(registros)}")
    
    for i, registro in enumerate(registros, 1):
        print(f"   Registro {i}: {registro['fuente'][:50]}...")
    
    print("\n")
    
    # 4. MOSTRAR ESTRUCTURA DE TABLAS
    print("4. Estructura de las tablas:")
    print("\n--- Tabla BaseLegal ---")
    print(base_legal_t.describe())
    
    print("\n--- Tabla ArticuloSEO ---")
    print(articulo_seo_t.describe())
    
    print("\n")
    
    # 5. INSTRUCCIONES PARA CONTINUAR
    print("5. Próximos pasos:")
    print("   a) Ejecutar: python RedactorConciliacionLaboral/agente_organizador.py")
    print("      - Esto clasificará y resumirá el contenido")
    print("   b) Ejecutar: python RedactorConciliacionLaboral/agente_redactor.py")
    print("      - Esto generará artículos SEO basados en el contenido")
    print("\n")
    
    print("=== DEMO COMPLETADO EXITOSAMENTE ===")
    print("El sistema está listo para procesar contenido legal y generar artículos SEO.")

if __name__ == "__main__":
    demo_completo()
