#!/usr/bin/env python3
"""
Demo simplificado del sistema de Redactor de Conciliación Laboral con Ollama
Versión directa sin columnas computadas complejas
"""

import pixeltable as pxt
from pixeltable.functions import ollama
from datetime import datetime, timezone
import json
import re

def verificar_ollama():
    """Verifica que Ollama esté funcionando"""
    try:
        response = ollama.chat(
            messages=[{"role": "user", "content": "Responde solo: OK"}],
            model="llama3.1:8b"
        )
        print("✓ Ollama está funcionando correctamente")
        return True
    except Exception as e:
        print(f"✗ Error con Ollama: {e}")
        return False

def demo_simple():
    print("=== DEMO SIMPLE: REDACTOR CON OLLAMA ===\n")
    
    # 1. Verificar Ollama
    if not verificar_ollama():
        return
    
    # 2. Configurar base de datos
    print("1. Configurando base de datos...")
    pxt.create_dir("redactor_legal", if_exists='ignore')
    
    base_legal_schema = {
        'fuente': pxt.String,
        'tipo_fuente': pxt.String,
        'texto_original': pxt.String,
        'tema': pxt.String,
        'resumen': pxt.String,
        'fecha_doc': pxt.String,
        'fecha_ingesta': pxt.Timestamp
    }
    
    articulo_seo_schema = {
        'id_fuente': pxt.Int,
        'tema': pxt.String,
        'titulo': pxt.String,
        'contenido_markdown': pxt.String,
        'fecha_creacion': pxt.Timestamp
    }
    
    base_legal_t = pxt.create_table("redactor_legal.BaseLegal", base_legal_schema, if_exists='replace')
    articulo_seo_t = pxt.create_table("redactor_legal.ArticuloSEO", articulo_seo_schema, if_exists='replace')
    
    print("✓ Tablas creadas\n")
    
    # 3. Ingresar datos de prueba
    print("2. Ingresando datos de prueba...")
    
    datos_prueba = [
        {
            'fuente': 'Código del Trabajo - Art. 159',
            'tipo_fuente': 'ley',
            'texto_original': 'El finiquito es el documento que acredita el término de la relación laboral y la liquidación de las prestaciones que correspondan al trabajador.',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        }
    ]
    
    base_legal_t.insert(datos_prueba)
    print("✓ Datos ingresados\n")
    
    # 4. Organizar contenido
    print("3. Organizando contenido con Ollama...")
    
    registros = base_legal_t.collect()
    for row in registros:
        if not row['tema']:
            try:
                # Prompt simple para organización
                prompt = f"""
Analiza este texto legal y responde SOLO con un JSON en este formato exacto:
{{"tema": "palabra_clave", "resumen": "resumen_breve"}}

Texto: {row['texto_original']}
"""
                
                response = ollama.chat(
                    messages=[{"role": "user", "content": prompt}],
                    model="llama3.1:8b"
                )
                
                resultado = response.choices[0].message.content
                print(f"Respuesta de Ollama: {resultado}")
                
                # Extraer JSON de forma más robusta
                json_match = re.search(r'\{[^}]*\}', resultado)
                if json_match:
                    json_str = json_match.group()
                    try:
                        datos = json.loads(json_str)
                        tema = datos.get('tema', 'derecho_laboral')
                        resumen = datos.get('resumen', row['texto_original'][:50] + '...')
                    except:
                        tema = 'finiquito'
                        resumen = 'Documento que acredita el término de la relación laboral'
                else:
                    tema = 'finiquito'
                    resumen = 'Documento que acredita el término de la relación laboral'
                
                # Actualizar registro
                base_legal_t.where(base_legal_t._id == row['_id']).update({
                    'tema': tema,
                    'resumen': resumen
                })
                
                print(f"✓ Organizado: tema='{tema}'\n")
                
            except Exception as e:
                print(f"✗ Error: {e}")
                # Valores por defecto
                base_legal_t.where(base_legal_t._id == row['_id']).update({
                    'tema': 'finiquito',
                    'resumen': 'Documento que acredita el término de la relación laboral'
                })
                print("✓ Organizado con valores por defecto\n")
    
    # 5. Generar artículos
    print("4. Generando artículos SEO con Ollama...")
    
    registros_organizados = base_legal_t.where(base_legal_t.tema != '').collect()
    
    for row in registros_organizados:
        try:
            prompt = f"""
Eres un redactor SEO experto. Crea un artículo en markdown sobre: {row['tema']}

Basándote en: {row['resumen']}

El artículo debe tener:
1. Un título con # (ejemplo: # ¿Qué es el finiquito laboral?)
2. Una introducción
3. Secciones con ## 
4. Una conclusión

Escribe en español y en formato markdown.
"""
            
            response = ollama.chat(
                messages=[{"role": "user", "content": prompt}],
                model="llama3.1:8b"
            )
            
            markdown = response.choices[0].message.content
            
            # Extraer título
            titulo_match = re.search(r'^#\s*(.+)$', markdown, re.MULTILINE)
            titulo = titulo_match.group(1) if titulo_match else f"Artículo sobre {row['tema']}"
            
            # Guardar artículo
            articulo_seo_t.insert({
                'id_fuente': row['_id'],
                'tema': row['tema'],
                'titulo': titulo,
                'contenido_markdown': markdown,
                'fecha_creacion': datetime.now(timezone.utc)
            })
            
            print(f"✓ Artículo generado: '{titulo}'\n")
            
        except Exception as e:
            print(f"✗ Error generando artículo: {e}\n")
    
    # 6. Mostrar resultados
    print("5. Resultados finales:")
    articulos = articulo_seo_t.collect()
    print(f"✓ Total de artículos generados: {len(articulos)}")
    
    for i, articulo in enumerate(articulos, 1):
        print(f"   {i}. {articulo['titulo']}")
        print(f"      Tema: {articulo['tema']}")
        print(f"      Contenido: {len(articulo['contenido_markdown'])} caracteres")
        print()
    
    # Mostrar un ejemplo de artículo
    if articulos:
        print("6. Ejemplo de artículo generado:")
        print("=" * 50)
        print(articulos[0]['contenido_markdown'][:500] + "...")
        print("=" * 50)
    
    print("\n=== DEMO COMPLETADO EXITOSAMENTE ===")
    print("El sistema funciona correctamente con Ollama!")

if __name__ == "__main__":
    demo_simple()
