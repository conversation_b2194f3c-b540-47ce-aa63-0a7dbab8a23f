#!/usr/bin/env python3
"""
Script para usar el Sistema de Redactor de Conciliación Laboral
Demuestra cómo agregar contenido y consultar resultados
"""

import pixeltable as pxt
from datetime import datetime, timezone

def mostrar_resultados():
    """Muestra los resultados del sistema"""
    print("=== CONSULTANDO RESULTADOS DEL SISTEMA ===\n")
    
    try:
        # Obtener tablas
        base_legal = pxt.get_table('redactor_ollama.base_legal')
        articulos_seo = pxt.get_table('redactor_ollama.articulos_seo')
        
        print("1. CONTENIDO BASE PROCESADO:")
        print("-" * 50)
        
        # Mostrar contenido base con procesamiento
        base_datos = base_legal.select(
            base_legal.fuente,
            base_legal.tema_extraido,
            base_legal.resumen_extraido
        ).collect()
        
        for i, item in enumerate(base_datos, 1):
            print(f"{i}. {item['fuente']}")
            print(f"   Tema: {item['tema_extraido']}")
            print(f"   Resumen: {item['resumen_extraido'][:80]}...")
            print()
        
        print("2. ARTÍCULOS SEO GENERADOS:")
        print("-" * 50)
        
        # Mostrar artículos finales
        articulos_datos = articulos_seo.collect()
        
        for i, articulo in enumerate(articulos_datos, 1):
            print(f"{i}. {articulo['titulo']}")
            print(f"   Tema: {articulo['tema']}")
            print(f"   Fuente: {articulo['fuente_original']}")
            print(f"   Tamaño: {len(articulo['contenido_markdown'])} caracteres")
            print(f"   Fecha: {articulo['fecha_generacion']}")
            print()
        
        return base_legal, articulos_seo
        
    except Exception as e:
        print(f"Error: {e}")
        print("Asegúrate de haber ejecutado primero: python sistema_completo_ollama.py")
        return None, None

def agregar_contenido_nuevo(base_legal):
    """Agrega nuevo contenido legal al sistema"""
    print("3. AGREGANDO NUEVO CONTENIDO:")
    print("-" * 50)
    
    nuevo_contenido = [
        {
            'fuente': 'Código del Trabajo Art. 184 - Despido',
            'texto_original': 'El empleador podrá poner término al contrato de trabajo invocando una o más de las causales establecidas en los artículos siguientes, siempre que se dé aviso al trabajador por escrito, con treinta días de anticipación, a lo menos.',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo Art. 67 - Remuneraciones',
            'texto_original': 'Las remuneraciones se devengan día a día. El pago de la remuneración se hará en moneda de curso legal, sin perjuicio de lo establecido en el artículo 10. Podrá pagarse con cheque o vale vista bancario a la orden del trabajador.',
            'fecha_ingesta': datetime.now(timezone.utc)
        }
    ]
    
    # Insertar nuevo contenido
    base_legal.insert(nuevo_contenido)
    print(f"✓ {len(nuevo_contenido)} nuevos textos legales agregados")
    
    # El sistema automáticamente procesará el nuevo contenido
    print("✓ El sistema procesará automáticamente el nuevo contenido con Ollama")
    print("✓ Se generarán temas, resúmenes y artículos SEO automáticamente")
    
    return len(nuevo_contenido)

def mostrar_articulo_completo(articulos_seo, indice=0):
    """Muestra un artículo completo"""
    print(f"\n4. EJEMPLO DE ARTÍCULO COMPLETO:")
    print("=" * 60)
    
    articulos = articulos_seo.collect()
    if articulos and len(articulos) > indice:
        articulo = articulos[indice]
        
        print(f"Título: {articulo['titulo']}")
        print(f"Tema: {articulo['tema']}")
        print(f"Fuente: {articulo['fuente_original']}")
        print(f"Resumen: {articulo['resumen']}")
        print(f"Fecha: {articulo['fecha_generacion']}")
        print("\nContenido completo:")
        print("-" * 40)
        print(articulo['contenido_markdown'])
        print("-" * 40)
    else:
        print("No hay artículos disponibles")

def consultas_avanzadas(base_legal, articulos_seo):
    """Demuestra consultas avanzadas"""
    print(f"\n5. CONSULTAS AVANZADAS:")
    print("-" * 50)
    
    # Buscar por tema específico
    print("a) Artículos sobre 'finiquito':")
    finiquito_articulos = articulos_seo.where(
        articulos_seo.tema.contains('Finiquito')
    ).collect()

    for articulo in finiquito_articulos:
        print(f"   - {articulo['titulo']}")
    
    # Contar artículos por tema
    print(f"\nb) Estadísticas por tema:")
    todos_articulos = articulos_seo.collect()
    temas = {}
    for articulo in todos_articulos:
        tema = articulo['tema'].lower()
        temas[tema] = temas.get(tema, 0) + 1
    
    for tema, cantidad in temas.items():
        print(f"   - {tema.capitalize()}: {cantidad} artículo(s)")
    
    # Artículos más recientes
    print(f"\nc) Artículos más recientes:")
    recientes = articulos_seo.order_by(
        articulos_seo.fecha_generacion, asc=False
    ).limit(3).collect()
    
    for articulo in recientes:
        print(f"   - {articulo['titulo']} ({articulo['fecha_generacion']})")

def main():
    print("=== USANDO EL SISTEMA DE REDACTOR LABORAL ===\n")
    
    # 1. Mostrar resultados existentes
    base_legal, articulos_seo = mostrar_resultados()
    
    if base_legal is None:
        return
    
    # 2. Agregar nuevo contenido (opcional)
    respuesta = input("\n¿Deseas agregar nuevo contenido legal? (s/n): ").lower()
    if respuesta == 's':
        nuevos = agregar_contenido_nuevo(base_legal)
        print(f"\n✓ Sistema actualizado con {nuevos} nuevos textos")
        
        # Esperar un momento para que se procese
        print("Esperando procesamiento automático...")
        import time
        time.sleep(2)
        
        # Actualizar datos
        base_legal, articulos_seo = mostrar_resultados()
    
    # 3. Mostrar artículo completo
    mostrar_articulo_completo(articulos_seo)
    
    # 4. Consultas avanzadas
    consultas_avanzadas(base_legal, articulos_seo)
    
    # 5. Instrucciones finales
    print(f"\n6. INSTRUCCIONES PARA USO CONTINUO:")
    print("=" * 60)
    print("Para seguir usando el sistema:")
    print("1. Agregar contenido: base_legal.insert([{'fuente': '...', 'texto_original': '...', 'fecha_ingesta': datetime.now(timezone.utc)}])")
    print("2. Consultar base: pxt.get_table('redactor_ollama.base_legal').collect()")
    print("3. Consultar artículos: pxt.get_table('redactor_ollama.articulos_seo').collect()")
    print("4. Buscar por tema: articulos_seo.where(articulos_seo.tema.contains('tema')).collect()")
    
    print(f"\n✅ El sistema está listo para uso continuo!")
    print("✅ Cada nuevo texto se procesará automáticamente con Ollama")
    print("✅ Los artículos SEO se generan en tiempo real")

if __name__ == "__main__":
    main()
