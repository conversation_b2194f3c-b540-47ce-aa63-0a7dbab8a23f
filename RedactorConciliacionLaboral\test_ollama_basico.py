#!/usr/bin/env python3
"""
Test básico para verificar que Ollama funciona con Pixeltable
"""

import pixeltable as pxt
from pixeltable.functions import ollama

def test_ollama_basico():
    print("=== TEST BÁSICO DE OLLAMA CON PIXELTABLE ===\n")
    
    # 1. Crear tabla simple
    print("1. Creando tabla de prueba...")
    pxt.create_dir("test_ollama", if_exists='ignore')
    
    schema = {
        'texto': pxt.String,
        'procesado': pxt.String
    }
    
    tabla = pxt.create_table("test_ollama.prueba", schema, if_exists='replace')
    print("✓ Tabla creada\n")
    
    # 2. Insertar datos
    print("2. Insertando datos de prueba...")
    datos = [
        {'texto': 'El finiquito es un documento laboral importante.', 'procesado': ''},
        {'texto': 'Las vacaciones son un derecho del trabajador.', 'procesado': ''}
    ]
    tabla.insert(datos)
    print("✓ Datos insertados\n")
    
    # 3. Crear UDF simple para Ollama
    print("3. Creando función UDF para Ollama...")
    
    @pxt.udf
    def procesar_con_ollama(texto: str) -> str:
        try:
            response = ollama.chat(
                messages=[{"role": "user", "content": f"Resume en una palabra el tema de: {texto}"}],
                model="llama3.1:8b"
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"Error: {str(e)}"
    
    print("✓ UDF creada\n")
    
    # 4. Agregar columna computada
    print("4. Agregando columna computada...")
    try:
        tabla.add_computed_column(tema_ollama=procesar_con_ollama(tabla.texto))
        print("✓ Columna computada agregada\n")
    except Exception as e:
        print(f"✗ Error agregando columna: {e}\n")
        return
    
    # 5. Consultar resultados
    print("5. Consultando resultados...")
    try:
        resultados = tabla.select(tabla.texto, tabla.tema_ollama).collect()
        
        for i, row in enumerate(resultados, 1):
            print(f"   {i}. Texto: {row['texto']}")
            print(f"      Tema: {row['tema_ollama']}")
            print()
        
        print("✓ Test completado exitosamente!")
        
    except Exception as e:
        print(f"✗ Error consultando: {e}")

if __name__ == "__main__":
    test_ollama_basico()
