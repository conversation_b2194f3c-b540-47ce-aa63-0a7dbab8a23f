#!/usr/bin/env python3
"""
Demo completo del sistema de Redactor de Conciliación Laboral con Ollama
Este script demuestra el flujo completo usando modelos locales:
1. Ingesta de contenido legal
2. Organización y clasificación con Ollama
3. Generación de artículos SEO con Ollama
"""

import pixeltable as pxt
from pixeltable.functions import ollama
from datetime import datetime, timezone
import json

def verificar_ollama():
    """Verifica que Ollama esté funcionando y que el modelo esté disponible"""
    try:
        response = ollama.chat(
            messages=[{"role": "user", "content": "<PERSON><PERSON>, ¿funcionas correctamente?"}],
            model="llama3.1:8b"
        )
        print("✓ Ollama está funcionando correctamente")
        return True
    except Exception as e:
        print(f"✗ Error con Ollama: {e}")
        print("Asegúrate de que:")
        print("1. Ollama esté instalado y ejecutándose")
        print("2. El modelo llama3.1:8b esté descargado (ollama pull llama3.1:8b)")
        return False

def demo_ollama_completo():
    print("=== DEMO COMPLETO: REDACTOR DE CONCILIACIÓN LABORAL CON OLLAMA ===\n")
    
    # 0. VERIFICAR OLLAMA
    print("0. Verificando Ollama...")
    if not verificar_ollama():
        return
    print()
    
    # 1. CONFIGURACIÓN INICIAL
    print("1. Configurando base de datos...")
    pxt.create_dir("redactor_legal", if_exists='ignore')
    
    # Esquemas de tablas
    base_legal_schema = {
        'fuente': pxt.String,
        'tipo_fuente': pxt.String,
        'texto_original': pxt.String,
        'tema': pxt.String,
        'resumen': pxt.String,
        'fecha_doc': pxt.String,
        'fecha_ingesta': pxt.Timestamp
    }
    
    articulo_seo_schema = {
        'id_fuente': pxt.Int,
        'tema': pxt.String,
        'titulo': pxt.String,
        'contenido_markdown': pxt.String,
        'fecha_creacion': pxt.Timestamp
    }
    
    # Crear tablas
    base_legal_t = pxt.create_table("redactor_legal.BaseLegal", base_legal_schema, if_exists='replace')
    articulo_seo_t = pxt.create_table("redactor_legal.ArticuloSEO", articulo_seo_schema, if_exists='replace')
    
    print("✓ Tablas creadas correctamente\n")
    
    # 2. INGESTA DE DATOS
    print("2. Ingresando datos de prueba...")
    
    datos_prueba = [
        {
            'fuente': 'Código del Trabajo - Art. 159',
            'tipo_fuente': 'ley',
            'texto_original': '''El finiquito es el documento que acredita el término de la relación laboral y la liquidación de las prestaciones que correspondan al trabajador. Debe contener la individualización de las partes, la causa del término del contrato, el detalle de las prestaciones pagadas y la firma de ambas partes.''',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        },
        {
            'fuente': 'Código del Trabajo - Art. 25',
            'tipo_fuente': 'ley',
            'texto_original': '''Las vacaciones anuales son un derecho irrenunciable del trabajador. Todo trabajador con más de un año de servicios tendrá derecho a un feriado anual de quince días hábiles, con remuneración íntegra que se pagará antes del inicio del período de vacaciones.''',
            'tema': '',
            'resumen': '',
            'fecha_doc': '2023-01-01',
            'fecha_ingesta': datetime.now(timezone.utc)
        }
    ]
    
    base_legal_t.insert(datos_prueba)
    print(f"✓ {len(datos_prueba)} registros ingresados\n")
    
    # 3. ORGANIZACIÓN CON OLLAMA
    print("3. Organizando contenido con Ollama...")
    
    def formatear_prompt_organizacion(texto: str):
        return f"""
Estás organizando contenido legal-laboral para crear una base de conocimiento SEO.
1) Indica el tema principal, un único tema breve (como: "vacaciones", "finiquito", etc.).
2) Proporciona un resumen de máximo 60 palabras.
3) Si encuentras una fecha en el texto (día, mes, año), indícalo en formato YYYY-MM-DD, si no, devuelve vacío.
Formato de salida JSON:
{{ "tema": "...", "resumen": "...", "fecha_doc": "YYYY-MM-DD" }}
Texto de referencia:
---
{texto}
---
"""
    
    # Usar una función UDF para procesar con Ollama
    @pxt.udf
    def procesar_texto_ollama(texto: str) -> str:
        prompt = formatear_prompt_organizacion(texto)
        try:
            response = ollama.chat(
                messages=[
                    {"role": "system", "content": "Actúa como organizador inteligente de textos legales laborales para SEO. Responde SOLO con JSON válido."},
                    {"role": "user", "content": prompt}
                ],
                model="llama3.1:8b"
            )
            return response.choices[0].message.content
        except Exception as e:
            return f'{{"tema": "error", "resumen": "Error procesando: {str(e)}", "fecha_doc": ""}}'

    # Agregar columna computada
    try:
        base_legal_t.add_computed_column(procesamiento=procesar_texto_ollama(base_legal_t.texto_original))
    except:
        pass  # La columna ya existe

    # Procesar registros
    registros = base_legal_t.select(base_legal_t._id, base_legal_t.texto_original, base_legal_t.tema, base_legal_t.resumen, base_legal_t.procesamiento).collect()

    for row in registros:
        if not row['tema'] or not row['resumen']:
            try:
                resultado_str = str(row['procesamiento'])
                print(f"Respuesta de Ollama: {resultado_str[:100]}...")

                # Intentar extraer JSON
                start = resultado_str.find('{')
                end = resultado_str.rfind('}') + 1
                if start != -1 and end != 0:
                    json_str = resultado_str[start:end]
                    # Limpiar el JSON
                    json_str = json_str.replace("'", '"')  # Reemplazar comillas simples
                    datos = json.loads(json_str)

                    # Actualizar registro
                    base_legal_t.where(base_legal_t._id == row['_id']).update({
                        'tema': datos.get('tema', ''),
                        'resumen': datos.get('resumen', ''),
                        'fecha_doc': datos.get('fecha_doc', '')
                    })
                    print(f"✓ Organizado: {datos.get('tema', 'Sin tema')}")
                else:
                    # Si no hay JSON, usar valores por defecto
                    tema_default = "derecho laboral"
                    resumen_default = row['texto_original'][:60] + "..."
                    base_legal_t.where(base_legal_t._id == row['_id']).update({
                        'tema': tema_default,
                        'resumen': resumen_default,
                        'fecha_doc': ''
                    })
                    print(f"✓ Organizado (valores por defecto): {tema_default}")

            except Exception as e:
                print(f"✗ Error organizando registro: {e}")
                # Usar valores por defecto en caso de error
                tema_default = "derecho laboral"
                resumen_default = row['texto_original'][:60] + "..."
                base_legal_t.where(base_legal_t._id == row['_id']).update({
                    'tema': tema_default,
                    'resumen': resumen_default,
                    'fecha_doc': ''
                })
                print(f"✓ Organizado (valores por defecto tras error): {tema_default}")
    
    print()
    
    # 4. GENERACIÓN DE ARTÍCULOS CON OLLAMA
    print("4. Generando artículos SEO con Ollama...")
    
    def prompt_articulo(texto: str, tema: str, resumen: str):
        return f"""
Eres redactor SEO experto en derecho laboral. Basándote en este contenido:
- Tema: {tema}
- Resumen: {resumen}

Genera un **artículo completo en Español**, incluyendo:
1. Título optimizado (<60 caracteres) con palabra clave, ej.: "¿Qué es el finiquito laboral?"
2. Introducción concisa.
3. Secciones con H2/H3, negritas y listas.
4. Una sección de preguntas frecuentes (FAQ).
5. Conclusión con llamado a acción (ej. "Para más guía consulta conciliacionlaboral.net").
Escribe en **markdown**.
Contenido de referencia:
---
{texto}
---
"""
    
    # Obtener registros organizados
    registros_organizados = base_legal_t.where(base_legal_t.tema != '').collect()
    
    for row in registros_organizados:
        try:
            prompt = prompt_articulo(row['texto_original'], row['tema'], row['resumen'])
            response = ollama.chat(
                messages=[
                    {"role": "system", "content": "Eres redactor SEO de artículos legales laborales para el sitio conciliacionlaboral.net."},
                    {"role": "user", "content": prompt}
                ],
                model="llama3.1:8b"
            )
            markdown = response.choices[0].message.content
            
            # Extraer título
            lines = markdown.splitlines()
            titulo = "Artículo Legal"
            for line in lines:
                if line.strip().startswith("# "):
                    titulo = line.strip("# ").strip()
                    break
            
            # Guardar artículo
            articulo_seo_t.insert({
                'id_fuente': row['_id'],
                'tema': row['tema'],
                'titulo': titulo,
                'contenido_markdown': markdown,
                'fecha_creacion': datetime.now(timezone.utc)
            })
            print(f"✓ Artículo generado: '{titulo}'")
            
        except Exception as e:
            print(f"✗ Error generando artículo para {row['_id']}: {e}")
    
    print()
    
    # 5. RESULTADOS
    print("5. Resultados finales:")
    articulos = articulo_seo_t.collect()
    print(f"✓ Total de artículos generados: {len(articulos)}")
    
    for i, articulo in enumerate(articulos, 1):
        print(f"   {i}. {articulo['titulo']} (Tema: {articulo['tema']})")
    
    print("\n=== DEMO COMPLETADO EXITOSAMENTE CON OLLAMA ===")
    print("El sistema está funcionando correctamente con modelos locales.")

if __name__ == "__main__":
    demo_ollama_completo()
